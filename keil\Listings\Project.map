LX51 LINKER/LOCATER V4.66.97.0                                                          07/12/2025  11:01:58  PAGE 1


LX51 LINKER/LOCATER V4.66.97.0, INVOKED BY:
D:\KEILC51\C51\BIN\LX51.EXE .\Objects\startup_cms8s6990.obj, .\Objects\adc.obj, .\Objects\epwm.obj, .\Objects\gpio.obj, 
>> .\Objects\system.obj, .\Objects\timer.obj, .\Objects\uart.obj, .\Objects\wdt.obj, .\Objects\flash.obj, .\Objects\ADC_
>> Init.obj, .\Objects\define.obj, .\Objects\GPIO_Init.obj, .\Objects\Timer_Init.obj, .\Objects\UART_Init.obj, .\Objects
>> \isr.obj, .\Objects\UART_Function.obj, .\Objects\Battery_Function.obj, .\Objects\Key.obj, .\Objects\ADC_Used.obj, .\O
>> bjects\main.obj TO .\Objects\Project PRINT (.\Listings\Project.map) REMOVEUNUSED


CPU MODE:     8051 MODE
MEMORY MODEL: LARGE WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\startup_cms8s6990.obj (?C_STARTUP)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  .\Objects\adc.obj (ADC)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\epwm.obj (EPWM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\gpio.obj (GPIO)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\system.obj (SYSTEM)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\timer.obj (TIMER)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\uart.obj (UART)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\wdt.obj (WDT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\flash.obj (FLASH)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Init.obj (ADC_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\define.obj (DEFINE)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\GPIO_Init.obj (GPIO_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Timer_Init.obj (TIMER_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Init.obj (UART_INIT)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\isr.obj (ISR)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\UART_Function.obj (UART_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Battery_Function.obj (BATTERY_FUNCTION)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\Key.obj (KEY)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\ADC_Used.obj (ADC_USED)
         COMMENT TYPE 0: C51 V9.60.0.0
  .\Objects\main.obj (MAIN)
         COMMENT TYPE 0: C51 V9.60.0.0
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPMUL)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FCAST)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (PRINTF)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPGETOPN)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPROUND)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPCONVERT)
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 2


         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FPADD)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51FPL.LIB (?C?FTNPWR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C_INIT)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?COPY)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CLDOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CSTOPTR)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?UIDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ILDIX)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?ULDIV)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LNEG)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?LSTKXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PLDIXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?PSTXDATA)
         COMMENT TYPE 1: A51 / ASM51 Assembler
  D:\KEILC51\C51\LIB\C51L.LIB (?C?CCASE)
         COMMENT TYPE 1: A51 / ASM51 Assembler


ACTIVE MEMORY CLASSES OF MODULE:  .\Objects\Project (?C_STARTUP)

BASE        START       END         USED      MEMORY CLASS
==========================================================
C:000000H   C:000000H   C:00FFFFH   0026C7H   CODE
I:000000H   I:000000H   I:0000FFH   000001H   IDATA
X:000000H   X:000000H   X:00FFFFH   0000C6H   XDATA
I:000020H.0 I:000020H.0 I:00002FH.7 000007H.2 BIT
C:000000H   C:000000H   C:00FFFFH   000028H   CONST
I:000000H   I:000000H   I:00007FH   00000DH   DATA


MEMORY MAP OF MODULE:  .\Objects\Project (?C_STARTUP)


START     STOP      LENGTH    ALIGN  RELOC    MEMORY CLASS   SEGMENT NAME
=========================================================================

* * * * * * * * * * *   D A T A   M E M O R Y   * * * * * * * * * * * * *
000000H   000007H   000008H   ---    AT..     DATA           "REG BANK 0"
000008H   00000CH   000005H   BYTE   UNIT     DATA           _DATA_GROUP_
00000DH.0 00001FH.7 000013H.0 ---    ---      **GAP**
000020H.0 000024H.3 000004H.4 BIT    UNIT     BIT            ?BI?MAIN
000024H.4 000026H.0 000001H.5 BIT    UNIT     BIT            _BIT_GROUP_
000026H.1 000026H.5 000000H.5 BIT    UNIT     BIT            ?BI?KEY
000026H.6 000027H.1 000000H.4 BIT    UNIT     BIT            ?BI?DEFINE
000027H.2 000027H   000000H.6 ---    ---      **GAP**
000028H   000028H   000001H   BYTE   UNIT     IDATA          ?STACK
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 3



* * * * * * * * * * *   C O D E   M E M O R Y   * * * * * * * * * * * * *
000000H   000002H   000003H   ---    OFFS..   CODE           ?CO??C_STARTUP?0
000003H   000005H   000003H   BYTE   OFFS..   CODE           ?ISR?00003
000006H   000009H   000004H   BYTE   UNIT     CODE           ?PR?ADC_START?ADC
00000AH   00000AH   000001H   BYTE   UNIT     CODE           ?PR?INT0_IRQHANDLER?ISR
00000BH   00000DH   000003H   BYTE   OFFS..   CODE           ?ISR?0000B
00000EH   000011H   000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUP?SYSTEM
000012H   000012H   000001H   BYTE   UNIT     CODE           ?PR?INT1_IRQHANDLER?ISR
000013H   000015H   000003H   BYTE   OFFS..   CODE           ?ISR?00013
000016H   000019H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_UNLOCK?FLASH
00001AH   00001AH   000001H   BYTE   UNIT     CODE           ?PR?TIMER2_IRQHANDLER?ISR
00001BH   00001DH   000003H   BYTE   OFFS..   CODE           ?ISR?0001B
00001EH   000021H   000004H   BYTE   UNIT     CODE           ?PR?FLASH_LOCK?FLASH
000022H   000022H   000001H   BYTE   UNIT     CODE           ?PR?UART1_IRQHANDLER?ISR
000023H   000025H   000003H   BYTE   OFFS..   CODE           ?ISR?00023
000026H   000026H   000001H   BYTE   UNIT     CODE           ?PR?P0EI_IRQHANDLER?ISR
000027H   000027H   000001H   BYTE   UNIT     CODE           ?PR?P3EI_IRQHANDLER?ISR
000028H   000028H   000001H   BYTE   UNIT     CODE           ?PR?LVD_IRQHANDLER?ISR
000029H   000029H   000001H   BYTE   UNIT     CODE           ?PR?LSE_IRQHANDLER?ISR
00002AH   00002AH   000001H   BYTE   UNIT     CODE           ?PR?ACMP_IRQHANDLER?ISR
00002BH   00002DH   000003H   BYTE   OFFS..   CODE           ?ISR?0002B
00002EH   00002EH   000001H   BYTE   UNIT     CODE           ?PR?TIMER3_IRQHANDLER?ISR
00002FH   00002FH   000001H   BYTE   UNIT     CODE           ?PR?TIMER4_IRQHANDLER?ISR
000030H   000030H   000001H   BYTE   UNIT     CODE           ?PR?EPWM_IRQHANDLER?ISR
000031H   000031H   000001H   BYTE   UNIT     CODE           ?PR?ADC_IRQHANDLER?ISR
000032H   000032H   000001H   BYTE   UNIT     CODE           ?PR?WDT_IRQHANDLER?ISR
000033H   000035H   000003H   BYTE   OFFS..   CODE           ?ISR?00033
000036H   000036H   000001H   BYTE   UNIT     CODE           ?PR?I2C_IRQHANDLER?ISR
000037H   000037H   000001H   BYTE   UNIT     CODE           ?PR?SPI_IRQHANDLER?ISR
000038H   00003AH   000003H   ---    ---      **GAP**
00003BH   00003DH   000003H   BYTE   OFFS..   CODE           ?ISR?0003B
00003EH   000042H   000005H   ---    ---      **GAP**
000043H   000045H   000003H   BYTE   OFFS..   CODE           ?ISR?00043
000046H   00004AH   000005H   ---    ---      **GAP**
00004BH   00004DH   000003H   BYTE   OFFS..   CODE           ?ISR?0004B
00004EH   000052H   000005H   ---    ---      **GAP**
000053H   000055H   000003H   BYTE   OFFS..   CODE           ?ISR?00053
000056H   000062H   00000DH   BYTE   UNIT     CODE           ?PR?_PUTCHAR?UART_INIT
000063H   000065H   000003H   BYTE   OFFS..   CODE           ?ISR?00063
000066H   00006AH   000005H   ---    ---      **GAP**
00006BH   00006DH   000003H   BYTE   OFFS..   CODE           ?ISR?0006B
00006EH   000072H   000005H   ---    ---      **GAP**
000073H   000075H   000003H   BYTE   OFFS..   CODE           ?ISR?00073
000076H   00007AH   000005H   ---    ---      **GAP**
00007BH   00007DH   000003H   BYTE   OFFS..   CODE           ?ISR?0007B
00007EH   000082H   000005H   ---    ---      **GAP**
000083H   000085H   000003H   BYTE   OFFS..   CODE           ?ISR?00083
000086H   000091H   00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERSTOP?SYSTEM
000092H   000092H   000001H   ---    ---      **GAP**
000093H   000095H   000003H   BYTE   OFFS..   CODE           ?ISR?00093
000096H   00009AH   000005H   ---    ---      **GAP**
00009BH   00009DH   000003H   BYTE   OFFS..   CODE           ?ISR?0009B
00009EH   0000A2H   000005H   ---    ---      **GAP**
0000A3H   0000A5H   000003H   BYTE   OFFS..   CODE           ?ISR?000A3
0000A6H   0000AAH   000005H   ---    ---      **GAP**
0000ABH   0000ADH   000003H   BYTE   OFFS..   CODE           ?ISR?000AB
0000AEH   0000B2H   000005H   ---    ---      **GAP**
0000B3H   0000B5H   000003H   BYTE   OFFS..   CODE           ?ISR?000B3
0000B6H   00091EH   000869H   BYTE   UNIT     CODE           ?PR?MAIN?MAIN
00091FH   001130H   000812H   BYTE   UNIT     CODE           ?C?LIB_CODE
001131H   0015B3H   000483H   BYTE   UNIT     CODE           ?PR?PRINTF?PRINTF
0015B4H   00171EH   00016BH   BYTE   UNIT     CODE           ?PR?KEY_INTERRUPT_PROCESS?MAIN
00171FH   001859H   00013BH   BYTE   UNIT     CODE           ?PR?_ADC?ADC_USED
00185AH   001983H   00012AH   BYTE   UNIT     CODE           ?PR?KEY_SCAN?KEY
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 4


001984H   001A9CH   000119H   BYTE   UNIT     CODE           ?PR?UART_DATA_PROCESS?UART_FUNCTION
001A9DH   001BACH   000110H   BYTE   UNIT     CODE           ?PR?LED_CONTROL?MAIN
001BADH   001CB5H   000109H   BYTE   UNIT     CODE           ?PR?TIMER0_IRQHANDLER?ISR
001CB6H   001D8AH   0000D5H   BYTE   UNIT     CODE           ?C_INITSEG
001D8BH   001E24H   00009AH   BYTE   UNIT     CODE           ?C_C51STARTUP
001E25H   001EB7H   000093H   BYTE   UNIT     CODE           ?PR?_MOTOR_STEP_CONTROL?MAIN
001EB8H   001F2FH   000078H   BYTE   UNIT     CODE           ?PR?P1EI_IRQHANDLER?ISR
001F30H   001FA7H   000078H   BYTE   UNIT     CODE           ?PR?P2EI_IRQHANDLER?ISR
001FA8H   00201AH   000073H   BYTE   UNIT     CODE           ?PR?_FUNCTION_UART_SEND_CMD?UART_FUNCTION
00201BH   00208AH   000070H   BYTE   UNIT     CODE           ?PR?GPIO_CONFIG?GPIO_INIT
00208BH   0020F7H   00006DH   BYTE   UNIT     CODE           ?PR?BATTERY_CHECK?MAIN
0020F8H   002156H   00005FH   BYTE   UNIT     CODE           ?PR?_UART_CONFIGRUNMODE?UART
002157H   0021B3H   00005DH   BYTE   UNIT     CODE           ?PR?_UART_SEND_STRING?UART_INIT
0021B4H   00220EH   00005BH   BYTE   UNIT     CODE           ?PR?UART0_IRQHANDLER?ISR
00220FH   00225DH   00004FH   BYTE   UNIT     CODE           ?PR?UART_0_CONFIG?UART_INIT
00225EH   0022A9H   00004CH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGRUNMODE?TIMER
0022AAH   0022F3H   00004AH   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERCLK?TIMER
0022F4H   00233AH   000047H   BYTE   UNIT     CODE           ?PR?_KEY_FUNCTION_SWITCH_SYSTEM?MAIN
00233BH   002379H   00003FH   BYTE   UNIT     CODE           ?PR?UART_1_CONFIG?UART_INIT
00237AH   0023B3H   00003AH   BYTE   UNIT     CODE           ?PR?KEY_BUFF_RETURN?KEY
0023B4H   0023E8H   000035H   BYTE   UNIT     CODE           ?PR?_FLASH_WRITE?FLASH
0023E9H   00241CH   000034H   BYTE   UNIT     CODE           ?PR?_FLASH_READ?FLASH
00241DH   00244DH   000031H   BYTE   UNIT     CODE           ?PR?_FLASH_ERASE?FLASH
00244EH   00247AH   00002DH   BYTE   UNIT     CODE           ?PR?_STORE_DLY?MAIN
00247BH   0024A4H   00002AH   BYTE   UNIT     CODE           ?PR?TMR0_CONFIG?TIMER_INIT
0024A5H   0024CDH   000029H   BYTE   UNIT     CODE           ?PR?TMR1_CONFIG?TIMER_INIT
0024CEH   0024F4H   000027H   BYTE   UNIT     CODE           ?PR?_TMR_CONFIGTIMERPERIOD?TIMER
0024F5H   00251AH   000026H   BYTE   UNIT     CODE           ?PR?RESTORE_DLY?MAIN
00251BH   00253FH   000025H   BYTE   UNIT     CODE           ?PR?ADC_GETADCRESULT?ADC
002540H   002564H   000025H   BYTE   UNIT     CODE           ?PR?_UART_GETRECEIVEINTFLAG?UART
002565H   002584H   000020H   BYTE   UNIT     CODE           ?PR?_UART_DATA_COPY?UART_FUNCTION
002585H   0025A4H   000020H   BYTE   UNIT     CODE           ?PR?UART_DATA_INIT?UART_FUNCTION
0025A5H   0025C4H   000020H   BYTE   UNIT     CODE           ?PR?_DELAY1MS?MAIN
0025C5H   0025E3H   00001FH   BYTE   UNIT     CODE           ?PR?_TMR_START?TIMER
0025E4H   002602H   00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARRECEIVEINTFLAG?UART
002603H   002620H   00001EH   BYTE   UNIT     CODE           ?PR?_ADC_ENABLECHANNEL?ADC
002621H   00263DH   00001DH   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEOVERFLOWINT?TIMER
00263EH   002657H   00001AH   BYTE   UNIT     CODE           ?PR?ADC_CONFIG?ADC_INIT
002658H   00266DH   000016H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGRUNMODE?ADC
00266EH   002681H   000014H   BYTE   UNIT     CODE           ?PR?_UART_GETBUFF?UART
002682H   002694H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEDOUBLEFREQUENCY?UART
002695H   0026A7H   000013H   BYTE   UNIT     CODE           ?PR?_UART_ENABLERECEIVE?UART
0026A8H   0026BAH   000013H   BYTE   UNIT     CODE           ?PR?GPIO_KEY_INTERRUPT_CONFIG?GPIO_INIT
0026BBH   0026CBH   000011H   BYTE   UNIT     CODE           ?PR?_UART_ENABLEINT?UART
0026CCH   0026D4H   000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCVREF?ADC
0026D5H   0026DDH   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTCLK?UART
0026DEH   0026E6H   000009H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBRTPERIOD?UART
0026E7H   0026EEH   000008H   BYTE   UNIT     CODE           ?PR?UART_ENABLEBRT?UART
0026EFH   0026F5H   000007H   BYTE   UNIT     CODE           ?PR?TIMER1_IRQHANDLER?ISR
0026F6H   0026FBH   000006H   BYTE   UNIT     CODE           ?PR?RETURN_UART_DATA_LENGTH?UART_FUNCTION
0026FCH   002701H   000006H   BYTE   UNIT     CODE           ?PR?CLEAN_UART_DATA_LENGTH?UART_FUNCTION
002702H   00271CH   00001BH   BYTE   UNIT     CONST          ?CO?ADC_USED
00271DH   002729H   00000DH   BYTE   UNIT     CONST          ?CO?UART_FUNCTION

* * * * * * * * * * *  X D A T A   M E M O R Y  * * * * * * * * * * * * *
000000H   00004FH   000050H   BYTE   UNIT     XDATA          _XDATA_GROUP_
000050H   00007AH   00002BH   BYTE   UNIT     XDATA          ?XD?MAIN
00007BH   00009BH   000021H   BYTE   UNIT     XDATA          ?XD?UART_FUNCTION
00009CH   0000B0H   000015H   BYTE   UNIT     XDATA          ?XD?DEFINE
0000B1H   0000BBH   00000BH   BYTE   UNIT     XDATA          ?XD?ADC_USED
0000BCH   0000C1H   000006H   BYTE   UNIT     XDATA          ?XD?KEY
0000C2H   0000C5H   000004H   BYTE   UNIT     XDATA          ?XD?ISR

* * * * * * * * *   R E M O V E D     S E G M E N T S   * * * * * * * *
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_STOP?ADC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 5


   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEHARDWARETRIG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEHARDWARETRIG?ADC
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGHARDWARETRIG?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGAN31?ADC
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_ADC_SETTRIGDELAYTIME?ADC
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGADCBRAKE?ADC
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_ADC_CONFIGCOMPAREVALUE?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETCMPRESULT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_ENABLEINT?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_DISABLEINT?ADC
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?ADC_GETINTFLAG?ADC
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?ADC_CLEARINTFLAG?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_ENABLELDO?ADC
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?ADC_DISABLELDO?ADC
   *DEL*:           000006H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGRUNMODE?EPWM
   *DEL*:           00005CH   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELCLK?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELPERIOD?EPWM
   *DEL*:           000080H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELSYMDUTY?EPWM
   *DEL*:           0000C2H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELASYMDUTY?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEONESHOTMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEAUTOLOADMODE?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_START?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_STOP?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEOUTPUT?EPWM
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEFAULTBRAKE?EPWM
   *DEL*:           00002BH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEFAULTBRAKE?EPWM
   *DEL*:           000015H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGCHANNELBRAKELEVEL?EPWM
   *DEL*:           000039H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDEADZONE?EPWM
   *DEL*:           00002CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDEADZONE?EPWM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEMASKCONTROL?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEMASKCONTROL?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEUPCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEUPCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETUPCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARUPCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEDOWNCMPINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEDOWNCMPINT?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETDOWNCMPINTFLAG?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARDOWNCMPINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEPERIODINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEPERIODINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARPERIODINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETPERIODINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEZEROINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEZEROINT?EPWM
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_EPWM_CLEARZEROINTFLAG?EPWM
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_EPWM_GETZEROINTFLAG?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_ENABLEFAULTBRAKEINT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLEFAULTBRAKEINT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?EPWM_GETFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_CLEARFAULTBRAKEINTFLAG?EPWM
   *DEL*:           000007H   BYTE   UNIT     CODE           ?PR?_EPWM_ENABLEREVERSEOUTPUT?EPWM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_EPWM_DISABLEREVERSEOUTPUT?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_TRIGSOFTWAREBRAKE?EPWM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?EPWM_DISABLESOFTWAREBRAKE?EPWM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_EPWM_CONFIGFBBRAKE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTENABLE?EPWM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?EPWM_ALLINTDISABLE?EPWM
   *DEL*:           0000DEH   BYTE   UNIT     CODE           ?PR?_GPIO_CONFIGGPIOMODE?GPIO
   *DEL*:           00001EH   BYTE   UNIT     CODE           ?PR?_GPIO_ENABLEINT?GPIO
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_GPIO_DISABLEINT?GPIO
   *DEL*:           000058H   BYTE   UNIT     CODE           ?PR?_GPIO_GETINTFLAG?GPIO
   *DEL*:           00004CH   BYTE   UNIT     CODE           ?PR?_GPIO_CLEARINTFLAG?GPIO
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVD?SYSTEM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 6


   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVD?SYSTEM
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGLVD?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_ENABLELVDINT?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_DISABLELVDINT?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_GETLVDINTFLAG?SYSTEM
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?SYS_CLEARLVDINTFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWDTRESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWDTRESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_CLEARWDTRESETFLAG?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_ENABLESOFTWARERESET?SYSTEM
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?SYS_DISABLESOFTWARERESET?SYSTEM
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?SYS_GETPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_CLEARPOWERONRESETFLAG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUP?SYSTEM
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?SYS_ENTERIDLE?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_ENABLEWAKEUPTRIG?SYSTEM
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?SYS_DISABLEWAKEUPTRIG?SYSTEM
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTCLK?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_SYS_CONFIGWUTTIME?SYSTEM
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_ENABLEGATE?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEGATE?TIMER
   *DEL*:           000044H   BYTE   UNIT     CODE           ?PR?_TMR_GETCOUNTVALUE?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_DISABLEOVERFLOWINT?TIMER
   *DEL*:           000033H   BYTE   UNIT     CODE           ?PR?_TMR_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           00001DH   BYTE   UNIT     CODE           ?PR?_TMR_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_TMR_STOP?TIMER
   *DEL*:           00000CH   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGRUNMODE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERCLK?TIMER
   *DEL*:           000021H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGTIMERPERIOD?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEGATE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEGATE?TIMER
   *DEL*:           00003DH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPARE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPARE?TIMER
   *DEL*:           000029H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREVALUE?TIMER
   *DEL*:           000009H   BYTE   UNIT     CODE           ?PR?_TMR2_CONFIGCOMPAREINTMODE?TIMER
   *DEL*:           00007BH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTURE?TIMER
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTURE?TIMER
   *DEL*:           00003CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREVALUE?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLEOVERFLOWINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLEOVERFLOWINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETOVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEAROVERFLOWINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_ENABLET2EXINT?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_DISABLET2EXINT?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_GETT2EXINTFLAG?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_CLEART2EXINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECOMPAREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECOMPAREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCOMPAREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCOMPAREINTFLAG?TIMER
   *DEL*:           00000EH   BYTE   UNIT     CODE           ?PR?_TMR2_ENABLECAPTUREINT?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_DISABLECAPTUREINT?TIMER
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?_TMR2_GETCAPTUREINTFLAG?TIMER
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_TMR2_CLEARCAPTUREINTFLAG?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTENABLE?TIMER
   *DEL*:           000003H   BYTE   UNIT     CODE           ?PR?TMR2_ALLINTDISABLE?TIMER
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?TMR2_START?TIMER
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?TMR2_STOP?TIMER
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEDOUBLEFREQUENCY?UART
   *DEL*:           000010H   BYTE   UNIT     CODE           ?PR?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000005H   BYTE   UNIT     XDATA          ?XD?_UART_CONFIGBAUDRATE?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_DISABLERECEIVE?UART
   *DEL*:           000011H   BYTE   UNIT     CODE           ?PR?_UART_DISABLEINT?UART
   *DEL*:           000025H   BYTE   UNIT     CODE           ?PR?_UART_GETSENDINTFLAG?UART
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 7


   *DEL*:           00001FH   BYTE   UNIT     CODE           ?PR?_UART_CLEARSENDINTFLAG?UART
   *DEL*:           000013H   BYTE   UNIT     CODE           ?PR?_UART_SENDBUFF?UART
   *DEL*:           000022H   BYTE   UNIT     CODE           ?PR?_UART_SENDNINTHBIT?UART
   *DEL*:           000017H   BYTE   UNIT     CODE           ?PR?_UART_GETNINTHBIT?UART
   *DEL*:           000008H   BYTE   UNIT     CODE           ?PR?UART_DISABLEBRT?UART
   *DEL*:           00000FH   BYTE   UNIT     CODE           ?PR?_WDT_CONFIGOVERFLOWTIME?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEARWDT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_ENABLEOVERFLOWINT?WDT
   *DEL*:           000004H   BYTE   UNIT     CODE           ?PR?WDT_DISABLEOVERFLOWINT?WDT
   *DEL*:           00000AH   BYTE   UNIT     CODE           ?PR?WDT_GETOVERFLOWINTFLAG?WDT
   *DEL*:           00001CH   BYTE   UNIT     CODE           ?PR?WDT_CLEAROVERFLOWINTFLAG?WDT
   *DEL*:           00001AH   BYTE   UNIT     CODE           ?PR?INIT_RAM_VARIANT?DEFINE
   *DEL*:           00000BH   BYTE   UNIT     CODE           ?PR?GETCHAR?UART_INIT
   *DEL*:           000016H   BYTE   UNIT     CODE           ?PR?_PUTS?UART_INIT
   *DEL*:           0000ADH   BYTE   UNIT     CODE           ?PR?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION
   *DEL*:           00000EH   BYTE   UNIT     XDATA          ?XD?_FUNCTION_STRCAT_PLUS_ASSIGN?UART_FUNCTION



OVERLAY MAP OF MODULE:   .\Objects\Project (?C_STARTUP)


FUNCTION/MODULE                                BIT_GROUP   DATA_GROUP   XDATA_GROUP
--> CALLED FUNCTION/MODULE                    START  STOP  START  STOP  START  STOP
===================================================================================
?C_C51STARTUP                                 ----- -----  ----- -----  ----- -----
  +--> MAIN/MAIN
  +--> ?C_INITSEG

MAIN/MAIN                                     24H.4 24H.7  ----- -----  0000H 0010H
  +--> GPIO_CONFIG/GPIO_INIT
  +--> _DELAY1MS/MAIN
  +--> ADC_CONFIG/ADC_INIT
  +--> UART_1_CONFIG/UART_INIT
  +--> UART_0_CONFIG/UART_INIT
  +--> TMR0_CONFIG/TIMER_INIT
  +--> TMR1_CONFIG/TIMER_INIT
  +--> RESTORE_DLY/MAIN
  +--> _STORE_DLY/MAIN
  +--> GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT
  +--> KEY_SCAN/KEY
  +--> KEY_BUFF_RETURN/KEY
  +--> _FUNCTION_UART_SEND_CMD/UART_FUNCTION
  +--> UART_DATA_INIT/UART_FUNCTION
  +--> BATTERY_CHECK/MAIN
  +--> LED_CONTROL/MAIN
  +--> _MOTOR_STEP_CONTROL/MAIN
  +--> KEY_INTERRUPT_PROCESS/MAIN
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN
  +--> UART_DATA_PROCESS/UART_FUNCTION
  +--> SYS_ENABLEWAKEUP/SYSTEM
  +--> SYS_ENTERSTOP/SYSTEM

GPIO_CONFIG/GPIO_INIT                         ----- -----  ----- -----  ----- -----

_DELAY1MS/MAIN                                ----- -----  ----- -----  ----- -----

ADC_CONFIG/ADC_INIT                           ----- -----  ----- -----  ----- -----
  +--> _ADC_CONFIGRUNMODE/ADC
  +--> _ADC_ENABLECHANNEL/ADC
  +--> _ADC_CONFIGADCVREF/ADC
  +--> ADC_START/ADC

_ADC_CONFIGRUNMODE/ADC                        ----- -----  ----- -----  ----- -----

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 8


_ADC_ENABLECHANNEL/ADC                        ----- -----  ----- -----  ----- -----

_ADC_CONFIGADCVREF/ADC                        ----- -----  ----- -----  ----- -----

ADC_START/ADC                                 ----- -----  ----- -----  ----- -----

UART_1_CONFIG/UART_INIT                       ----- -----  ----- -----  0011H 0016H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART

_UART_CONFIGRUNMODE/UART                      ----- -----  ----- -----  ----- -----

_UART_ENABLERECEIVE/UART                      ----- -----  ----- -----  ----- -----

_UART_CONFIGBRTCLK/UART                       ----- -----  ----- -----  ----- -----

_UART_ENABLEDOUBLEFREQUENCY/UART              ----- -----  ----- -----  ----- -----

_UART_CONFIGBRTPERIOD/UART                    ----- -----  ----- -----  ----- -----

UART_ENABLEBRT/UART                           ----- -----  ----- -----  ----- -----

UART_0_CONFIG/UART_INIT                       ----- -----  ----- -----  0011H 0016H
  +--> _UART_CONFIGRUNMODE/UART
  +--> _UART_ENABLERECEIVE/UART
  +--> _UART_CONFIGBRTCLK/UART
  +--> _UART_ENABLEDOUBLEFREQUENCY/UART
  +--> _UART_CONFIGBRTPERIOD/UART
  +--> UART_ENABLEBRT/UART
  +--> _UART_ENABLEINT/UART

_UART_ENABLEINT/UART                          ----- -----  ----- -----  ----- -----

TMR0_CONFIG/TIMER_INIT                        ----- -----  ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

_TMR_CONFIGRUNMODE/TIMER                      ----- -----  ----- -----  ----- -----

_TMR_CONFIGTIMERCLK/TIMER                     ----- -----  ----- -----  ----- -----

_TMR_CONFIGTIMERPERIOD/TIMER                  ----- -----  ----- -----  ----- -----

_TMR_ENABLEOVERFLOWINT/TIMER                  ----- -----  ----- -----  ----- -----

_TMR_START/TIMER                              ----- -----  ----- -----  ----- -----

TMR1_CONFIG/TIMER_INIT                        ----- -----  ----- -----  ----- -----
  +--> _TMR_CONFIGRUNMODE/TIMER
  +--> _TMR_CONFIGTIMERCLK/TIMER
  +--> _TMR_CONFIGTIMERPERIOD/TIMER
  +--> _TMR_ENABLEOVERFLOWINT/TIMER
  +--> _TMR_START/TIMER

RESTORE_DLY/MAIN                              ----- -----  ----- -----  0011H 0012H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_READ/FLASH
  +--> FLASH_LOCK/FLASH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 9



FLASH_UNLOCK/FLASH                            ----- -----  ----- -----  ----- -----

_FLASH_READ/FLASH                             ----- -----  ----- -----  ----- -----

FLASH_LOCK/FLASH                              ----- -----  ----- -----  ----- -----

_STORE_DLY/MAIN                               ----- -----  ----- -----  0011H 0012H
  +--> FLASH_UNLOCK/FLASH
  +--> _FLASH_ERASE/FLASH
  +--> _FLASH_WRITE/FLASH
  +--> FLASH_LOCK/FLASH

_FLASH_ERASE/FLASH                            ----- -----  ----- -----  ----- -----

_FLASH_WRITE/FLASH                            ----- -----  ----- -----  ----- -----

GPIO_KEY_INTERRUPT_CONFIG/GPIO_INIT           ----- -----  ----- -----  ----- -----

KEY_SCAN/KEY                                  ----- -----  ----- -----  ----- -----

KEY_BUFF_RETURN/KEY                           ----- -----  ----- -----  ----- -----

_FUNCTION_UART_SEND_CMD/UART_FUNCTION         ----- -----  ----- -----  0011H 001EH
  +--> _UART_SEND_STRING/UART_INIT

_UART_SEND_STRING/UART_INIT                   ----- -----  ----- -----  001FH 0023H

UART_DATA_INIT/UART_FUNCTION                  ----- -----  ----- -----  ----- -----

BATTERY_CHECK/MAIN                            ----- -----  ----- -----  ----- -----
  +--> _ADC/ADC_USED

_ADC/ADC_USED                                 ----- -----  ----- -----  0011H 001FH
  +--> _ADC_ENABLECHANNEL/ADC
  +--> ADC_GETADCRESULT/ADC
  +--> PRINTF/PRINTF

ADC_GETADCRESULT/ADC                          ----- -----  ----- -----  ----- -----

PRINTF/PRINTF                                 25H.0 26H.0  0008H 000CH  0020H 004FH
  +--> _PUTCHAR/UART_INIT

_PUTCHAR/UART_INIT                            ----- -----  ----- -----  ----- -----

LED_CONTROL/MAIN                              ----- -----  ----- -----  ----- -----

_MOTOR_STEP_CONTROL/MAIN                      ----- -----  ----- -----  ----- -----

KEY_INTERRUPT_PROCESS/MAIN                    ----- -----  ----- -----  ----- -----
  +--> _KEY_FUNCTION_SWITCH_SYSTEM/MAIN

_KEY_FUNCTION_SWITCH_SYSTEM/MAIN              ----- -----  ----- -----  ----- -----

UART_DATA_PROCESS/UART_FUNCTION               ----- -----  ----- -----  ----- -----
  +--> RETURN_UART_DATA_LENGTH/UART_FUNCTION
  +--> CLEAN_UART_DATA_LENGTH/UART_FUNCTION

RETURN_UART_DATA_LENGTH/UART_FUNCTION         ----- -----  ----- -----  ----- -----

CLEAN_UART_DATA_LENGTH/UART_FUNCTION          ----- -----  ----- -----  ----- -----

SYS_ENABLEWAKEUP/SYSTEM                       ----- -----  ----- -----  ----- -----

SYS_ENTERSTOP/SYSTEM                          ----- -----  ----- -----  ----- -----
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 10



?C_INITSEG                                    ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

INT0_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER0_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

INT1_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER1_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

UART0_IRQHANDLER/ISR                          ----- -----  ----- -----  ----- -----
  +--> _UART_GETRECEIVEINTFLAG/UART
  +--> _UART_GETBUFF/UART
  +--> _UART_DATA_COPY/UART_FUNCTION
  +--> _UART_CLEARRECEIVEINTFLAG/UART

_UART_GETRECEIVEINTFLAG/UART                  ----- -----  ----- -----  ----- -----

_UART_GETBUFF/UART                            ----- -----  ----- -----  ----- -----

_UART_DATA_COPY/UART_FUNCTION                 ----- -----  ----- -----  ----- -----

_UART_CLEARRECEIVEINTFLAG/UART                ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER2_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

UART1_IRQHANDLER/ISR                          ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P0EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P1EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P2EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

P3EI_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

LVD_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 11


LSE_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

ACMP_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER3_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

TIMER4_IRQHANDLER/ISR                         ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

EPWM_IRQHANDLER/ISR                           ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

ADC_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

WDT_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

I2C_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----

*** NEW ROOT ********************************

SPI_IRQHANDLER/ISR                            ----- -----  ----- -----  ----- -----



PUBLIC SYMBOLS OF MODULE:  .\Objects\Project (?C_STARTUP)


      VALUE       CLASS    TYPE      PUBLIC SYMBOL NAME
      =================================================
*DEL*:00000000H   XDATA    BYTE      ?_Function_Strcat_Plus_Assign?BYTE
      02000020H   XDATA    ---       ?_PRINTF?BYTE
      02000020H   XDATA    ---       ?_SPRINTF?BYTE
*DEL*:00000000H   XDATA    BYTE      ?_UART_ConfigBaudRate?BYTE
      0200001FH   XDATA    BYTE      ?_UART_Send_String?BYTE
      0100110BH   CODE     ---       ?C?CCASE
      01000F06H   CODE     ---       ?C?CLDOPTR
      01000EEDH   CODE     ---       ?C?CLDPTR
      00000000H   NUMBER   ---       ?C?CODESEG
      01000EC7H   CODE     ---       ?C?COPY
      01000F45H   CODE     ---       ?C?CSTOPTR
      01000F33H   CODE     ---       ?C?CSTPTR
      01000ACFH   CODE     ---       ?C?FCASTC
      01000ACAH   CODE     ---       ?C?FCASTI
      01000AC5H   CODE     ---       ?C?FCASTL
      01000C96H   CODE     ---       ?C?FPADD
      01000B8AH   CODE     ---       ?C?FPCONVERT
      01000A28H   CODE     ---       ?C?FPDIV
      01000B03H   CODE     ---       ?C?FPGETOPN2
      0100091FH   CODE     ---       ?C?FPMUL
      01000B38H   CODE     ---       ?C?FPNANRESULT
      01000B42H   CODE     ---       ?C?FPOVERFLOW
      01000B1AH   CODE     ---       ?C?FPRESULT
      01000B2EH   CODE     ---       ?C?FPRESULT2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 12


      01000B4DH   CODE     ---       ?C?FPROUND
      01000C92H   CODE     ---       ?C?FPSUB
      01000B3FH   CODE     ---       ?C?FPUNDERFLOW
      01000DB7H   CODE     ---       ?C?FTNPWR
      01000FBCH   CODE     ---       ?C?ILDIX
      010010A0H   CODE     ---       ?C?LNEG
      010010BAH   CODE     ---       ?C?LSTKXDATA
      010010AEH   CODE     ---       ?C?LSTXDATA
      010010EBH   CODE     ---       ?C?PLDIXDATA
      01001102H   CODE     ---       ?C?PSTXDATA
      01000F67H   CODE     ---       ?C?UIDIV
      0100100EH   CODE     ---       ?C?ULDIV
      00000000H   NUMBER   ---       ?C?XDATASEG
      01001DE0H   CODE     ---       ?C_START
      01000000H   CODE     ---       ?C_STARTUP
      0100171FH   CODE     ---       _ADC
*DEL*:00000000H   CODE     ---       _ADC_ConfigADCBrake
      010026CCH   CODE     ---       _ADC_ConfigADCVref
*DEL*:00000000H   CODE     ---       _ADC_ConfigAN31
*DEL*:00000000H   CODE     ---       _ADC_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _ADC_ConfigHardwareTrig
      01002658H   CODE     ---       _ADC_ConfigRunMode
      01002603H   CODE     ---       _ADC_EnableChannel
*DEL*:00000000H   CODE     ---       _ADC_SetTrigDelayTime
      010025A5H   CODE     ---       _Delay1ms
*DEL*:00000000H   CODE     ---       _EPWM_ClearDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ClearZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelAsymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelBrakeLevel
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelClk
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelPeriod
*DEL*:00000000H   CODE     ---       _EPWM_ConfigChannelSymDuty
*DEL*:00000000H   CODE     ---       _EPWM_ConfigFBBrake
*DEL*:00000000H   CODE     ---       _EPWM_ConfigRunMode
*DEL*:00000000H   CODE     ---       _EPWM_DisableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_DisableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_DisableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_DisableOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_DisableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_DisableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableAutoLoadMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableDeadZone
*DEL*:00000000H   CODE     ---       _EPWM_EnableDownCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableFaultBrake
*DEL*:00000000H   CODE     ---       _EPWM_EnableMaskControl
*DEL*:00000000H   CODE     ---       _EPWM_EnableOneShotMode
*DEL*:00000000H   CODE     ---       _EPWM_EnableOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnablePeriodInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableReverseOutput
*DEL*:00000000H   CODE     ---       _EPWM_EnableUpCmpInt
*DEL*:00000000H   CODE     ---       _EPWM_EnableZeroInt
*DEL*:00000000H   CODE     ---       _EPWM_GetDownCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetPeriodIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetUpCmpIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_GetZeroIntFlag
*DEL*:00000000H   CODE     ---       _EPWM_Start
*DEL*:00000000H   CODE     ---       _EPWM_Stop
      0100241DH   CODE     ---       _FLASH_Erase
      010023E9H   CODE     ---       _FLASH_Read
      010023B4H   CODE     ---       _FLASH_Write
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 13


*DEL*:00000000H   CODE     ---       _Function_Strcat_Plus_Assign
      01001FA8H   CODE     ---       _Function_UART_Send_CMD
*DEL*:00000000H   CODE     ---       _GPIO_ClearIntFlag
*DEL*:00000000H   CODE     ---       _GPIO_ConfigGPIOMode
*DEL*:00000000H   CODE     ---       _GPIO_DisableInt
*DEL*:00000000H   CODE     ---       _GPIO_EnableInt
*DEL*:00000000H   CODE     ---       _GPIO_GetIntFlag
      010022F4H   CODE     ---       _Key_Function_Switch_System
      01001E25H   CODE     ---       _Motor_Step_Control
      0100119CH   CODE     ---       _PRINTF
      01000056H   CODE     ---       _putchar
*DEL*:00000000H   CODE     ---       _puts
      01001196H   CODE     ---       _SPRINTF
      0100244EH   CODE     ---       _Store_dly
*DEL*:00000000H   CODE     ---       _SYS_ConfigLVD
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTCLK
*DEL*:00000000H   CODE     ---       _SYS_ConfigWUTTime
*DEL*:00000000H   CODE     ---       _TMR2_ClearCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ClearCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareIntMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigCompareValue
*DEL*:00000000H   CODE     ---       _TMR2_ConfigRunMode
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerClk
*DEL*:00000000H   CODE     ---       _TMR2_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR2_DisableCapture
*DEL*:00000000H   CODE     ---       _TMR2_DisableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompare
*DEL*:00000000H   CODE     ---       _TMR2_DisableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCapture
*DEL*:00000000H   CODE     ---       _TMR2_EnableCaptureInt
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompare
*DEL*:00000000H   CODE     ---       _TMR2_EnableCompareInt
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureIntFlag
*DEL*:00000000H   CODE     ---       _TMR2_GetCaptureValue
*DEL*:00000000H   CODE     ---       _TMR2_GetCompareIntFlag
*DEL*:00000000H   CODE     ---       _TMR_ClearOverflowIntFlag
      0100225EH   CODE     ---       _TMR_ConfigRunMode
      010022AAH   CODE     ---       _TMR_ConfigTimerClk
      010024CEH   CODE     ---       _TMR_ConfigTimerPeriod
*DEL*:00000000H   CODE     ---       _TMR_DisableGATE
*DEL*:00000000H   CODE     ---       _TMR_DisableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_EnableGATE
      01002621H   CODE     ---       _TMR_EnableOverflowInt
*DEL*:00000000H   CODE     ---       _TMR_GetCountValue
*DEL*:00000000H   CODE     ---       _TMR_GetOverflowIntFlag
      010025C5H   CODE     ---       _TMR_Start
*DEL*:00000000H   CODE     ---       _TMR_Stop
      010025E4H   CODE     ---       _UART_ClearReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_ClearSendIntFlag
*DEL*:00000000H   CODE     ---       _UART_ConfigBaudRate
      010026D5H   CODE     ---       _UART_ConfigBRTClk
      010026DEH   CODE     ---       _UART_ConfigBRTPeriod
      010020F8H   CODE     ---       _UART_ConfigRunMode
      01002565H   CODE     ---       _UART_Data_Copy
*DEL*:00000000H   CODE     ---       _UART_DisableDoubleFrequency
*DEL*:00000000H   CODE     ---       _UART_DisableInt
*DEL*:00000000H   CODE     ---       _UART_DisableReceive
      01002682H   CODE     ---       _UART_EnableDoubleFrequency
      010026BBH   CODE     ---       _UART_EnableInt
      01002695H   CODE     ---       _UART_EnableReceive
      0100266EH   CODE     ---       _UART_GetBuff
*DEL*:00000000H   CODE     ---       _UART_GetNinthBit
      01002540H   CODE     ---       _UART_GetReceiveIntFlag
*DEL*:00000000H   CODE     ---       _UART_GetSendIntFlag
      01002157H   CODE     ---       _UART_Send_String
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 14


*DEL*:00000000H   CODE     ---       _UART_SendBuff
*DEL*:00000000H   CODE     ---       _UART_SendNinthBit
*DEL*:00000000H   CODE     ---       _WDT_ConfigOverflowTime
*SFR* 000000D0H.6 DATA     BIT       AC
*SFR* 000000E0H   DATA     BYTE      ACC
      0100002AH   CODE     ---       ACMP_IRQHandler
*DEL*:00000000H   CODE     ---       ADC_ClearIntFlag
      0100263EH   CODE     ---       ADC_Config
*DEL*:00000000H   CODE     ---       ADC_DisableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_DisableInt
*DEL*:00000000H   CODE     ---       ADC_DisableLDO
*DEL*:00000000H   CODE     ---       ADC_EnableHardwareTrig
*DEL*:00000000H   CODE     ---       ADC_EnableInt
*DEL*:00000000H   CODE     ---       ADC_EnableLDO
      0100251BH   CODE     ---       ADC_GetADCResult
*DEL*:00000000H   CODE     ---       ADC_GetCmpResult
*DEL*:00000000H   CODE     ---       ADC_GetIntFlag
      01000031H   CODE     ---       ADC_IRQHandler
      01000006H   CODE     ---       ADC_Start
*DEL*:00000000H   CODE     ---       ADC_Stop
*SFR* 000000D1H   DATA     BYTE      ADCMPC
*SFR* 000000D5H   DATA     BYTE      ADCMPH
*SFR* 000000D4H   DATA     BYTE      ADCMPL
*SFR* 000000DFH   DATA     BYTE      ADCON0
*SFR* 000000DEH   DATA     BYTE      ADCON1
*SFR* 000000E9H   DATA     BYTE      ADCON2
*SFR* 000000D3H   DATA     BYTE      ADDLYL
*SFR* 000000DDH   DATA     BYTE      ADRESH
*SFR* 000000DCH   DATA     BYTE      ADRESL
      00000020H.1 BIT      BIT       auto_rotate_entry_complete
      00000021H.2 BIT      BIT       auto_rotate_flash
      02000060H   XDATA    WORD      auto_rotate_flash_timer
      00000021H.6 BIT      BIT       auto_rotate_mode
      00000020H.6 BIT      BIT       auto_rotate_running
*SFR* 000000F0H   DATA     BYTE      B
      00000020H.2 BIT      BIT       batlow
      00000021H.7 BIT      BIT       batlow1
      02000077H   XDATA    BYTE      batlow1_cnt
      02000065H   XDATA    BYTE      batlow_cnt
      020000ACH   XDATA    WORD      Battery_ADC_Wait_Time
      0100208BH   CODE     ---       Battery_Check
      02000078H   XDATA    WORD      BatV
      00000022H.7 BIT      BIT       Bit_1_ms_Buff
      00000023H.7 BIT      BIT       Bit_N_ms_Buff
      00000023H.3 BIT      BIT       Bit_Toggle
*SFR* 000000BFH   DATA     BYTE      BUZCON
*SFR* 000000BEH   DATA     BYTE      BUZDIV
*SFR* 000000C8H.5 DATA     BIT       CAPES
*SFR* 000000CEH   DATA     BYTE      CCEN
*SFR* 000000C3H   DATA     BYTE      CCH1
*SFR* 000000C5H   DATA     BYTE      CCH2
*SFR* 000000C7H   DATA     BYTE      CCH3
*SFR* 000000C2H   DATA     BYTE      CCL1
*SFR* 000000C4H   DATA     BYTE      CCL2
*SFR* 000000C6H   DATA     BYTE      CCL3
      00000026H.7 BIT      BIT       Center_Line_Control
      00000021H.5 BIT      BIT       Charg_State_Buff
      00000023H.1 BIT      BIT       charge_flash
      02000052H   XDATA    WORD      charge_flash_cnt
      00000022H.5 BIT      BIT       Charge_Was_Connected
*SFR* 0000008EH   DATA     BYTE      CKCON
      010026FCH   CODE     ---       Clean_UART_Data_Length
*SFR* 0000008FH   DATA     BYTE      CLKDIV
      02000066H   XDATA    INT       Count_1_Degree_Pulse
      020000A7H   XDATA    INT       Count_Toggle
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 15


*SFR* 000000D0H.7 DATA     BIT       CY
      0200009BH   XDATA    BYTE      Data_Length
      00000023H.2 BIT      BIT       Delay_Open
      00000027H.1 BIT      BIT       Delay_Over
      020000A9H   XDATA    WORD      Delay_Time
      020000A5H   XDATA    WORD      Delay_Time_Count
      00000024H.0 BIT      BIT       direction_changed
      0200006CH   XDATA    WORD      dly
*SFR* 00000083H   DATA     BYTE      DPH0
*SFR* 00000085H   DATA     BYTE      DPH1
*SFR* 00000082H   DATA     BYTE      DPL0
*SFR* 00000084H   DATA     BYTE      DPL1
*SFR* 00000086H   DATA     BYTE      DPS
*SFR* 00000093H   DATA     BYTE      DPX0
*SFR* 00000095H   DATA     BYTE      DPX1
*SFR* 000000A8H.7 DATA     BIT       EA
*SFR* 000000AAH   DATA     BYTE      EIE2
*SFR* 000000B2H   DATA     BYTE      EIF2
*SFR* 000000B9H   DATA     BYTE      EIP1
*SFR* 000000BAH   DATA     BYTE      EIP2
*DEL*:00000000H   CODE     ---       EPWM_AllIntDisable
*DEL*:00000000H   CODE     ---       EPWM_AllIntEnable
*DEL*:00000000H   CODE     ---       EPWM_ClearFaultBrakeIntFlag
*DEL*:00000000H   CODE     ---       EPWM_DisableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_DisableSoftwareBrake
*DEL*:00000000H   CODE     ---       EPWM_EnableFaultBrakeInt
*DEL*:00000000H   CODE     ---       EPWM_GetFaultBrakeIntFlag
      01000030H   CODE     ---       EPWM_IRQHandler
*DEL*:00000000H   CODE     ---       EPWM_TrigSoftwareBrake
*SFR* 000000A8H.4 DATA     BIT       ES0
*SFR* 000000A8H.6 DATA     BIT       ES1
*SFR* 000000A8H.1 DATA     BIT       ET0
*SFR* 000000A8H.3 DATA     BIT       ET1
*SFR* 000000A8H.5 DATA     BIT       ET2
*SFR* 000000A8H.0 DATA     BIT       EX0
*SFR* 000000A8H.2 DATA     BIT       EX1
*SFR* 000000D0H.5 DATA     BIT       F0
      0100001EH   CODE     ---       FLASH_Lock
      01000016H   CODE     ---       FLASH_UnLock
*SFR* 00000091H   DATA     BYTE      FUNCCR
      00000027H.0 BIT      BIT       Get_String_Buff
      020000ABH   XDATA    BYTE      Get_String_Wait_Time
*DEL*:00000000H   CODE     ---       getchar
      0100201BH   CODE     ---       GPIO_Config
      010026A8H   CODE     ---       GPIO_Key_Interrupt_Config
      01000036H   CODE     ---       I2C_IRQHandler
*SFR* 000000F6H   DATA     BYTE      I2CMBUF
*SFR* 000000F5H   DATA     BYTE      I2CMCR
*SFR* 000000F4H   DATA     BYTE      I2CMSA
*SFR* 000000F5H   DATA     BYTE      I2CMSR
*SFR* 000000F7H   DATA     BYTE      I2CMTP
*SFR* 000000F1H   DATA     BYTE      I2CSADR
*SFR* 000000F3H   DATA     BYTE      I2CSBUF
*SFR* 000000F2H   DATA     BYTE      I2CSCR
*SFR* 000000F2H   DATA     BYTE      I2CSSR
*SFR* 000000C8H.6 DATA     BIT       I3FR
*SFR* 000000A8H   DATA     BYTE      IE
*SFR* 00000088H.1 DATA     BIT       IE0
*SFR* 00000088H.3 DATA     BIT       IE1
*DEL*:00000000H   CODE     ---       Init_RAM_Variant
      0100000AH   CODE     ---       INT0_IRQHandler
      01000012H   CODE     ---       INT1_IRQHandler
*SFR* 000000B8H   DATA     BYTE      IP
*SFR* 00000088H.0 DATA     BIT       IT0
*SFR* 00000088H.2 DATA     BIT       IT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 16


      0200009EH   XDATA    BYTE      K1_cnt
      00000020H.7 BIT      BIT       K1_cnt_EN
      020000BDH   XDATA    BYTE      K1_Count
      00000026H.1 BIT      BIT       K1_Press
      0200009FH   XDATA    BYTE      K2_cnt
      00000021H.0 BIT      BIT       K2_cnt_EN
      020000BEH   XDATA    BYTE      K2_Count
      00000023H.4 BIT      BIT       k2_long_press_detected
      02000068H   XDATA    WORD      k2_long_press_timer
      00000026H.2 BIT      BIT       K2_Press
      00000022H.0 BIT      BIT       k2_released
      020000A0H   XDATA    BYTE      K3_cnt
      00000021H.1 BIT      BIT       K3_cnt_EN
      020000BFH   XDATA    BYTE      K3_Count
      00000026H.3 BIT      BIT       K3_Press
      00000022H.1 BIT      BIT       k3_released
      020000C0H   XDATA    BYTE      K4_Count
      00000026H.4 BIT      BIT       K4_Press
      020000C1H   XDATA    BYTE      K5_Count
      00000026H.5 BIT      BIT       K5_Press
      0200006AH   XDATA    WORD      key1_duration
      00000022H.2 BIT      BIT       key1_handle
      00000023H.5 BIT      BIT       key1_long_started
      02000058H   XDATA    WORD      key1_press_time
      00000022H.6 BIT      BIT       key1_pressed
      0200006EH   XDATA    WORD      key3_duration
      00000022H.3 BIT      BIT       key3_handle
      00000023H.6 BIT      BIT       key3_long_started
      0200005AH   XDATA    WORD      key3_press_time
      00000023H.0 BIT      BIT       key3_pressed
      020000BCH   XDATA    BYTE      Key_Buff
      0100237AH   CODE     ---       Key_Buff_Return
      00000020H.4 BIT      BIT       key_control_active
      010015B4H   CODE     ---       Key_Interrupt_Process
      00000020H.3 BIT      BIT       Key_Long_Press
      0100185AH   CODE     ---       Key_Scan
      00000024H.1 BIT      BIT       key_short_press_mode
      02000076H   XDATA    BYTE      last_direction
      01001A9DH   CODE     ---       LED_Control
      00000021H.4 BIT      BIT       led_flash_state
      02000063H   XDATA    WORD      led_flash_timer
      00000022H.4 BIT      BIT       ledonoff
      00000020H.0 BIT      BIT       ledonoff1
      02000062H   XDATA    BYTE      ledonoff1_cnt
      0200007AH   XDATA    BYTE      ledonoff_cnt
      00000024H.2 BIT      BIT       longhit
      0200009CH   XDATA    WORD      longhit_cnt
      01000029H   CODE     ---       LSE_IRQHandler
      01000028H   CODE     ---       LVD_IRQHandler
*SFR* 000000FDH   DATA     BYTE      MADRH
*SFR* 000000FCH   DATA     BYTE      MADRL
      010000B6H   CODE     ---       main
*SFR* 000000FFH   DATA     BYTE      MCTRL
*SFR* 000000FEH   DATA     BYTE      MDATA
*SFR* 000000FBH   DATA     BYTE      MLOCK
      020000B0H   XDATA    BYTE      Motor_Direction_Data
      00000020H.5 BIT      BIT       MOTOR_RUNNING_FLAG
      020000A3H   XDATA    WORD      Motor_Speed_Data
      00000021H.3 BIT      BIT       need_led_flash
      020000A1H   XDATA    INT       Num
      020000C2H   XDATA    INT       Num_Forward_Pulse
      020000C4H   XDATA    INT       Num_Reverse_Pulse
      02000073H   XDATA    WORD      original_speed
*SFR* 000000D0H.2 DATA     BIT       OV
*SFR* 000000D0H.0 DATA     BIT       P
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 17


*SFR* 00000080H   DATA     BYTE      P0
*SFR* 00000080H.0 DATA     BIT       P00
*SFR* 00000080H.1 DATA     BIT       P01
*SFR* 00000080H.2 DATA     BIT       P02
*SFR* 00000080H.3 DATA     BIT       P03
*SFR* 00000080H.4 DATA     BIT       P04
*SFR* 00000080H.5 DATA     BIT       P05
*SFR* 00000080H.6 DATA     BIT       P06
*SFR* 00000080H.7 DATA     BIT       P07
      01000026H   CODE     ---       P0EI_IRQHandler
*SFR* 000000ACH   DATA     BYTE      P0EXTIE
*SFR* 000000B4H   DATA     BYTE      P0EXTIF
*SFR* 0000009AH   DATA     BYTE      P0TRIS
*SFR* 00000090H   DATA     BYTE      P1
*SFR* 00000090H.0 DATA     BIT       P10
*SFR* 00000090H.1 DATA     BIT       P11
*SFR* 00000090H.2 DATA     BIT       P12
*SFR* 00000090H.3 DATA     BIT       P13
*SFR* 00000090H.4 DATA     BIT       P14
*SFR* 00000090H.5 DATA     BIT       P15
*SFR* 00000090H.6 DATA     BIT       P16
*SFR* 00000090H.7 DATA     BIT       P17
      01001EB8H   CODE     ---       P1EI_IRQHandler
*SFR* 000000ADH   DATA     BYTE      P1EXTIE
*SFR* 000000B5H   DATA     BYTE      P1EXTIF
*SFR* 000000A1H   DATA     BYTE      P1TRIS
*SFR* 000000A0H   DATA     BYTE      P2
*SFR* 000000A0H.0 DATA     BIT       P20
*SFR* 000000A0H.1 DATA     BIT       P21
*SFR* 000000A0H.2 DATA     BIT       P22
*SFR* 000000A0H.3 DATA     BIT       P23
*SFR* 000000A0H.4 DATA     BIT       P24
*SFR* 000000A0H.5 DATA     BIT       P25
*SFR* 000000A0H.6 DATA     BIT       P26
*SFR* 000000A0H.7 DATA     BIT       P27
      01001F30H   CODE     ---       P2EI_IRQHandler
*SFR* 000000AEH   DATA     BYTE      P2EXTIE
*SFR* 000000B6H   DATA     BYTE      P2EXTIF
*SFR* 000000A2H   DATA     BYTE      P2TRIS
*SFR* 000000B0H   DATA     BYTE      P3
*SFR* 000000B0H.0 DATA     BIT       P30
*SFR* 000000B0H.1 DATA     BIT       P31
*SFR* 000000B0H.2 DATA     BIT       P32
*SFR* 000000B0H.3 DATA     BIT       P33
*SFR* 000000B0H.4 DATA     BIT       P34
*SFR* 000000B0H.5 DATA     BIT       P35
*SFR* 000000B0H.6 DATA     BIT       P36
*SFR* 000000B0H.7 DATA     BIT       P37
      01000027H   CODE     ---       P3EI_IRQHandler
*SFR* 000000AFH   DATA     BYTE      P3EXTIE
*SFR* 000000B7H   DATA     BYTE      P3EXTIF
*SFR* 000000A3H   DATA     BYTE      P3TRIS
*SFR* 00000087H   DATA     BYTE      PCON
      00000026H.6 BIT      BIT       Power_count_clean
      020000AEH   XDATA    WORD      Power_Off_Wait_Time
*SFR* 000000B8H.4 DATA     BIT       PS0
*SFR* 000000B8H.6 DATA     BIT       PS1
*SFR* 000000D0H   DATA     BYTE      PSW
*SFR* 000000B8H.1 DATA     BIT       PT0
*SFR* 000000B8H.3 DATA     BIT       PT1
*SFR* 000000B8H.5 DATA     BIT       PT2
*SFR* 000000B8H.0 DATA     BIT       PX0
*SFR* 000000B8H.2 DATA     BIT       PX1
      010024F5H   CODE     ---       Restore_dly
      010026F6H   CODE     ---       Return_UART_Data_Length
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 18


*SFR* 00000098H.0 DATA     BIT       RI0
*SFR* 000000CBH   DATA     BYTE      RLDH
*SFR* 000000CAH   DATA     BYTE      RLDL
*SFR* 000000D0H.3 DATA     BIT       RS0
*SFR* 000000D0H.4 DATA     BIT       RS1
*SFR* 00000099H   DATA     BYTE      SBUF
*SFR* 00000099H   DATA     BYTE      SBUF0
*SFR* 000000EBH   DATA     BYTE      SBUF1
*SFR* 00000098H   DATA     BYTE      SCON0
*SFR* 000000EAH   DATA     BYTE      SCON1
      02000070H   XDATA    INT       Self_Check
*SFR* 00000081H   DATA     BYTE      SP
*SFR* 000000ECH   DATA     BYTE      SPCR
*SFR* 000000EEH   DATA     BYTE      SPDR
      00000024H.3 BIT      BIT       speedup
      0200005CH   XDATA    WORD      speedup_cnt
      01000037H   CODE     ---       SPI_IRQHandler
*SFR* 000000EDH   DATA     BYTE      SPSR
*SFR* 000000EFH   DATA     BYTE      SSCR
*DEL*:00000000H   CODE     ---       SYS_ClearLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_ClearPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_ClearWDTResetFlag
*DEL*:00000000H   CODE     ---       SYS_DisableLVD
*DEL*:00000000H   CODE     ---       SYS_DisableLVDInt
*DEL*:00000000H   CODE     ---       SYS_DisableSoftwareReset
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUp
*DEL*:00000000H   CODE     ---       SYS_DisableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_DisableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnableLVD
*DEL*:00000000H   CODE     ---       SYS_EnableLVDInt
*DEL*:00000000H   CODE     ---       SYS_EnableSoftwareReset
      0100000EH   CODE     ---       SYS_EnableWakeUp
*DEL*:00000000H   CODE     ---       SYS_EnableWakeUpTrig
*DEL*:00000000H   CODE     ---       SYS_EnableWDTReset
*DEL*:00000000H   CODE     ---       SYS_EnterIdle
      01000086H   CODE     ---       SYS_EnterStop
*DEL*:00000000H   CODE     ---       SYS_GetLVDIntFlag
*DEL*:00000000H   CODE     ---       SYS_GetPowerOnResetFlag
*DEL*:00000000H   CODE     ---       SYS_GetWDTResetFlag
      02000075H   XDATA    BYTE      System_Mode_Before_Charge
      02000072H   XDATA    BYTE      System_Mode_Data
      02000054H   XDATA    DWORD     Systemclock
*SFR* 000000C8H.2 DATA     BIT       T2CM
*SFR* 000000C8H   DATA     BYTE      T2CON
*SFR* 000000C8H.0 DATA     BIT       T2I0
*SFR* 000000C8H.1 DATA     BIT       T2I1
*SFR* 000000CFH   DATA     BYTE      T2IE
*SFR* 000000C9H   DATA     BYTE      T2IF
*SFR* 000000C8H.7 DATA     BIT       T2PS
*SFR* 000000C8H.3 DATA     BIT       T2R0
*SFR* 000000C8H.4 DATA     BIT       T2R1
*SFR* 000000D2H   DATA     BYTE      T34MOD
*SFR* 00000096H   DATA     BYTE      TA
*SFR* 00000088H   DATA     BYTE      TCON
*SFR* 00000088H.5 DATA     BIT       TF0
*SFR* 00000088H.7 DATA     BIT       TF1
*SFR* 0000008CH   DATA     BYTE      TH0
*SFR* 0000008DH   DATA     BYTE      TH1
*SFR* 000000CDH   DATA     BYTE      TH2
*SFR* 000000DBH   DATA     BYTE      TH3
*SFR* 000000E3H   DATA     BYTE      TH4
*SFR* 00000098H.1 DATA     BIT       TI0
      01001BADH   CODE     ---       Timer0_IRQHandler
      010026EFH   CODE     ---       Timer1_IRQHandler
      0100001AH   CODE     ---       Timer2_IRQHandler
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 19


      0100002EH   CODE     ---       Timer3_IRQHandler
      0100002FH   CODE     ---       Timer4_IRQHandler
      0200005EH   XDATA    WORD      timer_1ms_count
*SFR* 0000008AH   DATA     BYTE      TL0
*SFR* 0000008BH   DATA     BYTE      TL1
*SFR* 000000CCH   DATA     BYTE      TL2
*SFR* 000000DAH   DATA     BYTE      TL3
*SFR* 000000E2H   DATA     BYTE      TL4
*SFR* 00000089H   DATA     BYTE      TMOD
      0100247BH   CODE     ---       TMR0_Config
      010024A5H   CODE     ---       TMR1_Config
*DEL*:00000000H   CODE     ---       TMR2_AllIntDisable
*DEL*:00000000H   CODE     ---       TMR2_AllIntEnable
*DEL*:00000000H   CODE     ---       TMR2_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_ClearT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_DisableGATE
*DEL*:00000000H   CODE     ---       TMR2_DisableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_DisableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_EnableGATE
*DEL*:00000000H   CODE     ---       TMR2_EnableOverflowInt
*DEL*:00000000H   CODE     ---       TMR2_EnableT2EXInt
*DEL*:00000000H   CODE     ---       TMR2_GetOverflowIntFlag
*DEL*:00000000H   CODE     ---       TMR2_GetT2EXIntFlag
*DEL*:00000000H   CODE     ---       TMR2_Start
*DEL*:00000000H   CODE     ---       TMR2_Stop
*SFR* 00000088H.4 DATA     BIT       TR0
*SFR* 00000088H.6 DATA     BIT       TR1
*SFR* 00000098H.2 DATA     BIT       U0RB8
*SFR* 00000098H.4 DATA     BIT       U0REN
*SFR* 00000098H.7 DATA     BIT       U0SM0
*SFR* 00000098H.6 DATA     BIT       U0SM1
*SFR* 00000098H.5 DATA     BIT       U0SM2
*SFR* 00000098H.3 DATA     BIT       U0TB8
      010021B4H   CODE     ---       UART0_IRQHandler
      01000022H   CODE     ---       UART1_IRQHandler
      0100220FH   CODE     ---       UART_0_Config
      0100233BH   CODE     ---       UART_1_Config
      01002585H   CODE     ---       UART_Data_Init
      01001984H   CODE     ---       UART_Data_Process
*DEL*:00000000H   CODE     ---       UART_DisableBRT
      010026E7H   CODE     ---       UART_EnableBRT
      0200007BH   XDATA    ---       UART_Get_String
*SFR* 00000097H   DATA     BYTE      WDCON
*DEL*:00000000H   CODE     ---       WDT_ClearOverflowIntFlag
*DEL*:00000000H   CODE     ---       WDT_ClearWDT
*DEL*:00000000H   CODE     ---       WDT_DisableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_EnableOverflowInt
*DEL*:00000000H   CODE     ---       WDT_GetOverflowIntFlag
      01000032H   CODE     ---       WDT_IRQHandler
*SFR* 000000BDH   DATA     BYTE      WUTCRH
*SFR* 000000BCH   DATA     BYTE      WUTCRL



SYMBOL TABLE OF MODULE:  .\Objects\Project (?C_STARTUP)

      VALUE       REP       CLASS    TYPE      SYMBOL NAME
      ====================================================
      ---         MODULE    ---      ---       ?C_STARTUP
      01000000H   PUBLIC    CODE     ---       ?C_STARTUP
      000000E0H   SYMBOL    DATA     ---       ACC
      000000F0H   SYMBOL    DATA     ---       B
      00000083H   SYMBOL    DATA     ---       DPH
      00000082H   SYMBOL    DATA     ---       DPL
      00000000H   SYMBOL    NUMBER   ---       IBPSTACK
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 20


      00000100H   SYMBOL    NUMBER   ---       IBPSTACKTOP
      00000100H   SYMBOL    NUMBER   ---       IDATALEN
      01001D8EH   SYMBOL    CODE     ---       IDATALOOP
      00000000H   SYMBOL    NUMBER   ---       PBPSTACK
      00000100H   SYMBOL    NUMBER   ---       PBPSTACKTOP
      00000000H   SYMBOL    NUMBER   ---       PDATALEN
      00000000H   SYMBOL    NUMBER   ---       PDATASTART
      00000000H   SYMBOL    NUMBER   ---       PPAGE
      00000000H   SYMBOL    NUMBER   ---       PPAGEENABLE
      000000A0H   SYMBOL    DATA     ---       PPAGE_SFR
      00000081H   SYMBOL    DATA     ---       SP
      01001D8BH   SYMBOL    CODE     ---       STARTUP1
      00000000H   SYMBOL    NUMBER   ---       XBPSTACK
      00000000H   SYMBOL    NUMBER   ---       XBPSTACKTOP
      00000400H   SYMBOL    NUMBER   ---       XDATALEN
      01001D99H   SYMBOL    CODE     ---       XDATALOOP
      00000000H   SYMBOL    NUMBER   ---       XDATASTART
      01000000H   LINE      CODE     ---       #126
      01001D8BH   LINE      CODE     ---       #133
      01001D8DH   LINE      CODE     ---       #134
      01001D8EH   LINE      CODE     ---       #135
      01001D8FH   LINE      CODE     ---       #136
      01001D91H   LINE      CODE     ---       #140
      01001D94H   LINE      CODE     ---       #141
      01001D96H   LINE      CODE     ---       #145
      01001D98H   LINE      CODE     ---       #147
      01001D99H   LINE      CODE     ---       #148
      01001D9AH   LINE      CODE     ---       #149
      01001D9BH   LINE      CODE     ---       #150
      01001D9DH   LINE      CODE     ---       #151
      01001D9FH   LINE      CODE     ---       #185
      01001DA2H   LINE      CODE     ---       #196

      ---         MODULE    ---      ---       ADC
      010026CCH   PUBLIC    CODE     ---       _ADC_ConfigADCVref
      0100251BH   PUBLIC    CODE     ---       ADC_GetADCResult
      01002603H   PUBLIC    CODE     ---       _ADC_EnableChannel
      01002658H   PUBLIC    CODE     ---       _ADC_ConfigRunMode
      01000006H   PUBLIC    CODE     ---       ADC_Start
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 21


      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 22


      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 23


      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000006H   BLOCK     CODE     ---       LVL=0
      01000006H   LINE      CODE     ---       #66
      01000006H   LINE      CODE     ---       #67
      01000006H   LINE      CODE     ---       #68
      01000009H   LINE      CODE     ---       #69
      ---         BLOCKEND  ---      ---       LVL=0

      01002658H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCClkDiv
      00000005H   SYMBOL    DATA     BYTE      ADCResultTpye
      01002658H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002658H   LINE      CODE     ---       #88
      01002658H   LINE      CODE     ---       #89
      01002658H   LINE      CODE     ---       #90
      01002658H   LINE      CODE     ---       #92
      0100265AH   LINE      CODE     ---       #93
      0100265DH   LINE      CODE     ---       #94
      0100265EH   LINE      CODE     ---       #95
      01002660H   LINE      CODE     ---       #97
      01002662H   LINE      CODE     ---       #98
      01002666H   LINE      CODE     ---       #99
      0100266BH   LINE      CODE     ---       #100
      0100266DH   LINE      CODE     ---       #101
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCTGSource
      00000005H   SYMBOL    DATA     BYTE      TGMode
      00000006H   SYMBOL    DATA     BYTE      Temp

      01002603H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCChannel
      01002603H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      01002603H   LINE      CODE     ---       #154
      01002603H   LINE      CODE     ---       #155
      01002603H   LINE      CODE     ---       #156
      01002603H   LINE      CODE     ---       #158
      01002605H   LINE      CODE     ---       #159
      01002609H   LINE      CODE     ---       #160
      01002612H   LINE      CODE     ---       #161
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 24


      01002614H   LINE      CODE     ---       #163
      01002616H   LINE      CODE     ---       #164
      0100261AH   LINE      CODE     ---       #165
      0100261EH   LINE      CODE     ---       #166
      01002620H   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      An31Channel
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      TrigTime
      00000005H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      ADCBrake
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000006H   SYMBOL    DATA     WORD      ADCCompareValue

      0100251BH   BLOCK     CODE     ---       LVL=0
      0100251BH   LINE      CODE     ---       #258
      0100251BH   LINE      CODE     ---       #259
      0100251BH   LINE      CODE     ---       #260
      01002522H   LINE      CODE     ---       #261
      01002522H   LINE      CODE     ---       #262
      01002535H   LINE      CODE     ---       #263
      01002535H   LINE      CODE     ---       #264
      0100253FH   LINE      CODE     ---       #265
      ---         BLOCKEND  ---      ---       LVL=0

      010026CCH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADCVref
      010026CCH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010026CCH   LINE      CODE     ---       #344
      010026CCH   LINE      CODE     ---       #345
      010026CCH   LINE      CODE     ---       #346
      010026CCH   LINE      CODE     ---       #348
      010026D0H   LINE      CODE     ---       #349
      010026D2H   LINE      CODE     ---       #350
      010026D3H   LINE      CODE     ---       #351
      010026D4H   LINE      CODE     ---       #353
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       EPWM
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 25


      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 26


      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 27


      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      EpwmRunModeMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000005H   SYMBOL    DATA     BYTE      ClkDiv
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Period
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      Duty
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000004H   SYMBOL    DATA     WORD      UpCmp
      00000002H   SYMBOL    DATA     WORD      DowmCmp
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000005H   SYMBOL    DATA     BYTE      CountMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      BrakeSource
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      BrakeLevel
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000005H   SYMBOL    DATA     BYTE      DeadTime
      00000007H   SYMBOL    DATA     BYTE      Channel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000005H   SYMBOL    DATA     BYTE      MaskLevel
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 28


      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelNum
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      ChannelMask
      00000007H   SYMBOL    DATA     BYTE      FBBrakeLevel
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       GPIO
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 29


      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 30


      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000003H   SYMBOL    DATA     BYTE      PinMode
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinMSK
      00000007H   SYMBOL    DATA     BYTE      Port
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 31


      00000005H   SYMBOL    DATA     BYTE      PinNum
      00000006H   SYMBOL    DATA     BYTE      PinIntFlag
      00000007H   SYMBOL    DATA     BYTE      Port
      00000005H   SYMBOL    DATA     BYTE      PinNum

      ---         MODULE    ---      ---       SYSTEM
      01000086H   PUBLIC    CODE     ---       SYS_EnterStop
      0100000EH   PUBLIC    CODE     ---       SYS_EnableWakeUp
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 32


      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 33


      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      00000007H   SYMBOL    DATA     BYTE      LVDValue
      00000006H   SYMBOL    DATA     BYTE      Temp

      0100000EH   BLOCK     CODE     ---       LVL=0
      0100000EH   LINE      CODE     ---       #333
      0100000EH   LINE      CODE     ---       #334
      0100000EH   LINE      CODE     ---       #335
      01000011H   LINE      CODE     ---       #336
      ---         BLOCKEND  ---      ---       LVL=0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 34



      01000086H   BLOCK     CODE     ---       LVL=0
      01000086H   LINE      CODE     ---       #358
      01000086H   LINE      CODE     ---       #359
      01000086H   LINE      CODE     ---       #360
      01000087H   LINE      CODE     ---       #361
      01000088H   LINE      CODE     ---       #362
      0100008BH   LINE      CODE     ---       #363
      0100008CH   LINE      CODE     ---       #364
      0100008DH   LINE      CODE     ---       #365
      0100008EH   LINE      CODE     ---       #366
      0100008FH   LINE      CODE     ---       #367
      01000090H   LINE      CODE     ---       #368
      01000091H   LINE      CODE     ---       #369
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      clkdiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      time
      00000003H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       TIMER
      010025C5H   PUBLIC    CODE     ---       _TMR_Start
      01002621H   PUBLIC    CODE     ---       _TMR_EnableOverflowInt
      010024CEH   PUBLIC    CODE     ---       _TMR_ConfigTimerPeriod
      010022AAH   PUBLIC    CODE     ---       _TMR_ConfigTimerClk
      0100225EH   PUBLIC    CODE     ---       _TMR_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 35


      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 36


      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 37


      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100225EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerMode
      00000003H   SYMBOL    DATA     BYTE      TimerModeBranch
      0100225EH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      0100225EH   LINE      CODE     ---       #74
      0100225EH   LINE      CODE     ---       #75
      0100225EH   LINE      CODE     ---       #76
      0100225EH   LINE      CODE     ---       #78
      0100226DH   LINE      CODE     ---       #79
      0100226DH   LINE      CODE     ---       #80
      0100226DH   LINE      CODE     ---       #81
      0100226FH   LINE      CODE     ---       #82
      01002273H   LINE      CODE     ---       #83
      01002279H   LINE      CODE     ---       #84
      01002279H   LINE      CODE     ---       #85
      0100227BH   LINE      CODE     ---       #86
      0100227BH   LINE      CODE     ---       #87
      0100227DH   LINE      CODE     ---       #88
      01002281H   LINE      CODE     ---       #89
      0100228EH   LINE      CODE     ---       #90
      01002290H   LINE      CODE     ---       #91
      01002291H   LINE      CODE     ---       #92
      01002291H   LINE      CODE     ---       #93
      01002293H   LINE      CODE     ---       #94
      01002296H   LINE      CODE     ---       #95
      01002297H   LINE      CODE     ---       #96
      01002299H   LINE      CODE     ---       #97
      0100229AH   LINE      CODE     ---       #98
      0100229AH   LINE      CODE     ---       #99
      0100229CH   LINE      CODE     ---       #100
      010022A0H   LINE      CODE     ---       #101
      010022A7H   LINE      CODE     ---       #102
      010022A9H   LINE      CODE     ---       #103
      010022A9H   LINE      CODE     ---       #104
      010022A9H   LINE      CODE     ---       #105
      010022A9H   LINE      CODE     ---       #106
      010022A9H   LINE      CODE     ---       #107
      ---         BLOCKEND  ---      ---       LVL=0

      010022AAH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerClkDiv
      010022AAH   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010022AAH   LINE      CODE     ---       #117
      010022AAH   LINE      CODE     ---       #118
      010022AAH   LINE      CODE     ---       #119
      010022AAH   LINE      CODE     ---       #121
      010022B9H   LINE      CODE     ---       #122
      010022B9H   LINE      CODE     ---       #123
      010022B9H   LINE      CODE     ---       #124
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 38


      010022BBH   LINE      CODE     ---       #125
      010022BFH   LINE      CODE     ---       #126
      010022C5H   LINE      CODE     ---       #127
      010022C5H   LINE      CODE     ---       #128
      010022C7H   LINE      CODE     ---       #129
      010022C7H   LINE      CODE     ---       #130
      010022C9H   LINE      CODE     ---       #131
      010022CDH   LINE      CODE     ---       #132
      010022D2H   LINE      CODE     ---       #133
      010022D4H   LINE      CODE     ---       #134
      010022D5H   LINE      CODE     ---       #135
      010022D5H   LINE      CODE     ---       #136
      010022D7H   LINE      CODE     ---       #137
      010022DBH   LINE      CODE     ---       #138
      010022E0H   LINE      CODE     ---       #139
      010022E0H   LINE      CODE     ---       #140
      010022E2H   LINE      CODE     ---       #141
      010022E2H   LINE      CODE     ---       #142
      010022E4H   LINE      CODE     ---       #143
      010022E8H   LINE      CODE     ---       #144
      010022F1H   LINE      CODE     ---       #145
      010022F3H   LINE      CODE     ---       #146
      010022F3H   LINE      CODE     ---       #147
      010022F3H   LINE      CODE     ---       #148
      010022F3H   LINE      CODE     ---       #149
      010022F3H   LINE      CODE     ---       #150
      ---         BLOCKEND  ---      ---       LVL=0

      010024CEH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000005H   SYMBOL    DATA     BYTE      TimerPeriodHigh
      00000003H   SYMBOL    DATA     BYTE      TimerPeriodLow
      010024CEH   LINE      CODE     ---       #160
      010024CEH   LINE      CODE     ---       #161
      010024CEH   LINE      CODE     ---       #162
      010024DDH   LINE      CODE     ---       #163
      010024DDH   LINE      CODE     ---       #164
      010024DDH   LINE      CODE     ---       #165
      010024DFH   LINE      CODE     ---       #166
      010024E1H   LINE      CODE     ---       #167
      010024E2H   LINE      CODE     ---       #168
      010024E2H   LINE      CODE     ---       #169
      010024E4H   LINE      CODE     ---       #170
      010024E6H   LINE      CODE     ---       #171
      010024E7H   LINE      CODE     ---       #172
      010024E7H   LINE      CODE     ---       #173
      010024E9H   LINE      CODE     ---       #174
      010024EBH   LINE      CODE     ---       #175
      010024ECH   LINE      CODE     ---       #176
      010024ECH   LINE      CODE     ---       #177
      010024F0H   LINE      CODE     ---       #178
      010024F4H   LINE      CODE     ---       #179
      010024F4H   LINE      CODE     ---       #180
      010024F4H   LINE      CODE     ---       #181
      010024F4H   LINE      CODE     ---       #182
      010024F4H   LINE      CODE     ---       #183
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern

      01002621H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      01002621H   LINE      CODE     ---       #256
      01002621H   LINE      CODE     ---       #257
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 39


      01002621H   LINE      CODE     ---       #258
      01002630H   LINE      CODE     ---       #259
      01002630H   LINE      CODE     ---       #260
      01002630H   LINE      CODE     ---       #261
      01002632H   LINE      CODE     ---       #262
      01002633H   LINE      CODE     ---       #263
      01002633H   LINE      CODE     ---       #264
      01002635H   LINE      CODE     ---       #265
      01002636H   LINE      CODE     ---       #266
      01002636H   LINE      CODE     ---       #267
      01002639H   LINE      CODE     ---       #268
      0100263AH   LINE      CODE     ---       #269
      0100263AH   LINE      CODE     ---       #270
      0100263DH   LINE      CODE     ---       #271
      0100263DH   LINE      CODE     ---       #272
      0100263DH   LINE      CODE     ---       #273
      0100263DH   LINE      CODE     ---       #274
      0100263DH   LINE      CODE     ---       #275
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000006H   SYMBOL    DATA     BYTE      IntFlag
      00000007H   SYMBOL    DATA     BYTE      Timern

      010025C5H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      010025C5H   LINE      CODE     ---       #368
      010025C5H   LINE      CODE     ---       #369
      010025C5H   LINE      CODE     ---       #370
      010025D4H   LINE      CODE     ---       #371
      010025D4H   LINE      CODE     ---       #372
      010025D4H   LINE      CODE     ---       #373
      010025D7H   LINE      CODE     ---       #374
      010025D8H   LINE      CODE     ---       #375
      010025D8H   LINE      CODE     ---       #376
      010025DBH   LINE      CODE     ---       #377
      010025DCH   LINE      CODE     ---       #378
      010025DCH   LINE      CODE     ---       #379
      010025DFH   LINE      CODE     ---       #380
      010025E0H   LINE      CODE     ---       #381
      010025E0H   LINE      CODE     ---       #382
      010025E3H   LINE      CODE     ---       #383
      010025E3H   LINE      CODE     ---       #384
      010025E3H   LINE      CODE     ---       #385
      010025E3H   LINE      CODE     ---       #386
      010025E3H   LINE      CODE     ---       #387
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Timern
      00000007H   SYMBOL    DATA     BYTE      Timer2Mode
      00000005H   SYMBOL    DATA     BYTE      Timer2LoadMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      TimerClkDiv
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000004H   SYMBOL    DATA     WORD      TimerPeriod
      00000007H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      CompareMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000003H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CompareValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CompareIntMode
      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000005H   SYMBOL    DATA     BYTE      Timer2CaptureMode
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 40


      00000006H   SYMBOL    DATA     BYTE      Temp
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000004H   SYMBOL    DATA     WORD      CaputerValue
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn
      00000007H   SYMBOL    DATA     BYTE      Timer2CCn

      ---         MODULE    ---      ---       UART
      010026DEH   PUBLIC    CODE     ---       _UART_ConfigBRTPeriod
      010026D5H   PUBLIC    CODE     ---       _UART_ConfigBRTClk
      010026E7H   PUBLIC    CODE     ---       UART_EnableBRT
      0100266EH   PUBLIC    CODE     ---       _UART_GetBuff
      010025E4H   PUBLIC    CODE     ---       _UART_ClearReceiveIntFlag
      01002540H   PUBLIC    CODE     ---       _UART_GetReceiveIntFlag
      010026BBH   PUBLIC    CODE     ---       _UART_EnableInt
      01002695H   PUBLIC    CODE     ---       _UART_EnableReceive
      01002682H   PUBLIC    CODE     ---       _UART_EnableDoubleFrequency
      010020F8H   PUBLIC    CODE     ---       _UART_ConfigRunMode
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 41


      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 42


      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 43


      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010020F8H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTMode
      00000003H   SYMBOL    DATA     BYTE      UARTBaudTimer
      010020F8H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010020F8H   LINE      CODE     ---       #70
      010020F8H   LINE      CODE     ---       #71
      010020F8H   LINE      CODE     ---       #72
      010020F8H   LINE      CODE     ---       #74
      010020FBH   LINE      CODE     ---       #75
      010020FBH   LINE      CODE     ---       #76
      010020FDH   LINE      CODE     ---       #77
      01002101H   LINE      CODE     ---       #78
      01002108H   LINE      CODE     ---       #79
      0100210AH   LINE      CODE     ---       #81
      0100210DH   LINE      CODE     ---       #82
      01002119H   LINE      CODE     ---       #83
      01002119H   LINE      CODE     ---       #84
      01002119H   LINE      CODE     ---       #85
      01002119H   LINE      CODE     ---       #86
      01002119H   LINE      CODE     ---       #87
      0100211CH   LINE      CODE     ---       #88
      0100211EH   LINE      CODE     ---       #89
      0100211EH   LINE      CODE     ---       #90
      01002121H   LINE      CODE     ---       #91
      01002123H   LINE      CODE     ---       #92
      01002123H   LINE      CODE     ---       #93
      01002126H   LINE      CODE     ---       #94
      01002126H   LINE      CODE     ---       #95
      01002126H   LINE      CODE     ---       #96
      01002126H   LINE      CODE     ---       #97
      01002126H   LINE      CODE     ---       #99
      01002126H   LINE      CODE     ---       #100
      0100212BH   LINE      CODE     ---       #101
      0100212BH   LINE      CODE     ---       #102
      0100212DH   LINE      CODE     ---       #103
      01002131H   LINE      CODE     ---       #104
      0100213AH   LINE      CODE     ---       #105
      0100213CH   LINE      CODE     ---       #107
      0100213FH   LINE      CODE     ---       #108
      0100214BH   LINE      CODE     ---       #109
      0100214BH   LINE      CODE     ---       #110
      0100214BH   LINE      CODE     ---       #111
      0100214BH   LINE      CODE     ---       #112
      0100214BH   LINE      CODE     ---       #113
      0100214EH   LINE      CODE     ---       #114
      0100214FH   LINE      CODE     ---       #115
      0100214FH   LINE      CODE     ---       #116
      01002152H   LINE      CODE     ---       #117
      01002153H   LINE      CODE     ---       #118
      01002153H   LINE      CODE     ---       #119
      01002156H   LINE      CODE     ---       #120
      01002156H   LINE      CODE     ---       #121
      01002156H   LINE      CODE     ---       #122
      01002156H   LINE      CODE     ---       #123
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 44


      01002156H   LINE      CODE     ---       #124
      01002156H   LINE      CODE     ---       #125
      ---         BLOCKEND  ---      ---       LVL=0

      01002682H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002682H   LINE      CODE     ---       #133
      01002682H   LINE      CODE     ---       #134
      01002682H   LINE      CODE     ---       #135
      01002688H   LINE      CODE     ---       #136
      01002688H   LINE      CODE     ---       #137
      0100268BH   LINE      CODE     ---       #138
      0100268BH   LINE      CODE     ---       #139
      01002691H   LINE      CODE     ---       #140
      01002691H   LINE      CODE     ---       #141
      01002694H   LINE      CODE     ---       #142
      01002694H   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002695H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      01002695H   LINE      CODE     ---       #280
      01002695H   LINE      CODE     ---       #281
      01002695H   LINE      CODE     ---       #282
      0100269BH   LINE      CODE     ---       #283
      0100269BH   LINE      CODE     ---       #284
      0100269EH   LINE      CODE     ---       #285
      0100269EH   LINE      CODE     ---       #286
      010026A4H   LINE      CODE     ---       #287
      010026A4H   LINE      CODE     ---       #288
      010026A7H   LINE      CODE     ---       #289
      010026A7H   LINE      CODE     ---       #290
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010026BBH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010026BBH   LINE      CODE     ---       #317
      010026BBH   LINE      CODE     ---       #318
      010026BBH   LINE      CODE     ---       #319
      010026C1H   LINE      CODE     ---       #320
      010026C1H   LINE      CODE     ---       #321
      010026C3H   LINE      CODE     ---       #322
      010026C3H   LINE      CODE     ---       #323
      010026C9H   LINE      CODE     ---       #324
      010026C9H   LINE      CODE     ---       #325
      010026CBH   LINE      CODE     ---       #326
      010026CBH   LINE      CODE     ---       #327
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn

      01002540H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      01002540H   LINE      CODE     ---       #353
      01002542H   LINE      CODE     ---       #354
      01002542H   LINE      CODE     ---       #355
      01002548H   LINE      CODE     ---       #356
      01002548H   LINE      CODE     ---       #357
      01002552H   LINE      CODE     ---       #358
      01002552H   LINE      CODE     ---       #359
      01002558H   LINE      CODE     ---       #360
      01002558H   LINE      CODE     ---       #361
      01002562H   LINE      CODE     ---       #362
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 45


      01002562H   LINE      CODE     ---       #363
      01002564H   LINE      CODE     ---       #364
      ---         BLOCKEND  ---      ---       LVL=0

      010025E4H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      010025E4H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      temp
      ---         BLOCKEND  ---      ---       LVL=1
      010025E4H   LINE      CODE     ---       #373
      010025E4H   LINE      CODE     ---       #374
      010025E4H   LINE      CODE     ---       #377
      010025EAH   LINE      CODE     ---       #378
      010025EAH   LINE      CODE     ---       #379
      010025ECH   LINE      CODE     ---       #380
      010025EFH   LINE      CODE     ---       #381
      010025F3H   LINE      CODE     ---       #382
      010025F3H   LINE      CODE     ---       #383
      010025F9H   LINE      CODE     ---       #384
      010025F9H   LINE      CODE     ---       #385
      010025FBH   LINE      CODE     ---       #386
      010025FEH   LINE      CODE     ---       #387
      01002602H   LINE      CODE     ---       #388
      01002602H   LINE      CODE     ---       #389
      ---         BLOCKEND  ---      ---       LVL=0
      00000006H   SYMBOL    DATA     BYTE      UARTn
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000006H   SYMBOL    DATA     BYTE      temp

      0100266EH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      0100266EH   LINE      CODE     ---       #443
      0100266EH   LINE      CODE     ---       #444
      0100266EH   LINE      CODE     ---       #445
      01002674H   LINE      CODE     ---       #446
      01002674H   LINE      CODE     ---       #447
      01002677H   LINE      CODE     ---       #448
      01002677H   LINE      CODE     ---       #449
      0100267FH   LINE      CODE     ---       #450
      0100267FH   LINE      CODE     ---       #451
      01002681H   LINE      CODE     ---       #452
      01002681H   LINE      CODE     ---       #453
      01002681H   LINE      CODE     ---       #454
      ---         BLOCKEND  ---      ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn
      00000005H   SYMBOL    DATA     BYTE      UARTSendValue
      00000007H   SYMBOL    DATA     BYTE      UARTn

      010026E7H   BLOCK     CODE     ---       LVL=0
      010026E7H   LINE      CODE     ---       #534
      010026E7H   LINE      CODE     ---       #535
      010026E7H   LINE      CODE     ---       #536
      010026EEH   LINE      CODE     ---       #537
      ---         BLOCKEND  ---      ---       LVL=0

      010026D5H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      BRTClkDiv
      010026D5H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Temp
      ---         BLOCKEND  ---      ---       LVL=1
      010026D5H   LINE      CODE     ---       #544
      010026D5H   LINE      CODE     ---       #545
      010026D5H   LINE      CODE     ---       #546
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 46


      010026D5H   LINE      CODE     ---       #548
      010026D9H   LINE      CODE     ---       #549
      010026DBH   LINE      CODE     ---       #550
      010026DCH   LINE      CODE     ---       #551
      010026DDH   LINE      CODE     ---       #552
      ---         BLOCKEND  ---      ---       LVL=0

      010026DEH   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      BRTPeriod
      010026DEH   LINE      CODE     ---       #560
      010026DEH   LINE      CODE     ---       #561
      010026DEH   LINE      CODE     ---       #562
      010026E3H   LINE      CODE     ---       #563
      010026E6H   LINE      CODE     ---       #564
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       WDT
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 47


      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 48


      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 49


      00000007H   SYMBOL    DATA     BYTE      TsysCoefficient
      00000006H   SYMBOL    DATA     BYTE      Temp

      ---         MODULE    ---      ---       FLASH
      0100241DH   PUBLIC    CODE     ---       _FLASH_Erase
      010023E9H   PUBLIC    CODE     ---       _FLASH_Read
      010023B4H   PUBLIC    CODE     ---       _FLASH_Write
      0100001EH   PUBLIC    CODE     ---       FLASH_Lock
      01000016H   PUBLIC    CODE     ---       FLASH_UnLock
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 50


      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 51


      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      01000016H   BLOCK     CODE     ---       LVL=0
      01000016H   LINE      CODE     ---       #68
      01000016H   LINE      CODE     ---       #69
      01000016H   LINE      CODE     ---       #70
      01000019H   LINE      CODE     ---       #71
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 52


      0100001EH   BLOCK     CODE     ---       LVL=0
      0100001EH   LINE      CODE     ---       #79
      0100001EH   LINE      CODE     ---       #80
      0100001EH   LINE      CODE     ---       #81
      01000021H   LINE      CODE     ---       #82
      ---         BLOCKEND  ---      ---       LVL=0

      010023B4H   BLOCK     CODE     ---       LVL=0
      00000002H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      00000003H   SYMBOL    DATA     BYTE      Data
      010023B4H   LINE      CODE     ---       #95
      010023B6H   LINE      CODE     ---       #96
      010023B6H   LINE      CODE     ---       #97
      010023BAH   LINE      CODE     ---       #98
      010023BCH   LINE      CODE     ---       #99
      010023BFH   LINE      CODE     ---       #101
      010023C2H   LINE      CODE     ---       #102
      010023C2H   LINE      CODE     ---       #103
      010023C4H   LINE      CODE     ---       #104
      010023C5H   LINE      CODE     ---       #105
      010023CAH   LINE      CODE     ---       #106
      010023CBH   LINE      CODE     ---       #107
      010023CCH   LINE      CODE     ---       #108
      010023CDH   LINE      CODE     ---       #109
      010023CEH   LINE      CODE     ---       #110
      010023CFH   LINE      CODE     ---       #111
      010023D0H   LINE      CODE     ---       #112
      010023D5H   LINE      CODE     ---       #113
      010023D7H   LINE      CODE     ---       #114
      010023D8H   LINE      CODE     ---       #116
      010023D8H   LINE      CODE     ---       #117
      010023DDH   LINE      CODE     ---       #118
      010023DEH   LINE      CODE     ---       #119
      010023DFH   LINE      CODE     ---       #120
      010023E0H   LINE      CODE     ---       #121
      010023E1H   LINE      CODE     ---       #122
      010023E2H   LINE      CODE     ---       #123
      010023E3H   LINE      CODE     ---       #124
      010023E8H   LINE      CODE     ---       #125
      010023E8H   LINE      CODE     ---       #126
      ---         BLOCKEND  ---      ---       LVL=0

      010023E9H   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      010023E9H   LINE      CODE     ---       #138
      010023EBH   LINE      CODE     ---       #139
      010023EBH   LINE      CODE     ---       #140
      010023EDH   LINE      CODE     ---       #141
      010023F0H   LINE      CODE     ---       #142
      010023F3H   LINE      CODE     ---       #143
      010023F3H   LINE      CODE     ---       #144
      010023F5H   LINE      CODE     ---       #145
      010023F6H   LINE      CODE     ---       #146
      010023FBH   LINE      CODE     ---       #147
      010023FCH   LINE      CODE     ---       #148
      010023FDH   LINE      CODE     ---       #149
      010023FEH   LINE      CODE     ---       #150
      010023FFH   LINE      CODE     ---       #151
      01002400H   LINE      CODE     ---       #152
      01002401H   LINE      CODE     ---       #153
      01002406H   LINE      CODE     ---       #154
      01002408H   LINE      CODE     ---       #155
      0100240AH   LINE      CODE     ---       #157
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 53


      0100240AH   LINE      CODE     ---       #158
      0100240FH   LINE      CODE     ---       #159
      01002410H   LINE      CODE     ---       #160
      01002411H   LINE      CODE     ---       #161
      01002412H   LINE      CODE     ---       #162
      01002413H   LINE      CODE     ---       #163
      01002414H   LINE      CODE     ---       #164
      01002415H   LINE      CODE     ---       #165
      0100241AH   LINE      CODE     ---       #166
      0100241AH   LINE      CODE     ---       #167
      0100241CH   LINE      CODE     ---       #168
      ---         BLOCKEND  ---      ---       LVL=0

      0100241DH   BLOCK     CODE     ---       LVL=0
      00000003H   SYMBOL    DATA     BYTE      FLASHModule
      00000004H   SYMBOL    DATA     WORD      Addr
      0100241DH   LINE      CODE     ---       #179
      0100241FH   LINE      CODE     ---       #180
      0100241FH   LINE      CODE     ---       #181
      01002421H   LINE      CODE     ---       #182
      01002424H   LINE      CODE     ---       #183
      01002427H   LINE      CODE     ---       #184
      01002427H   LINE      CODE     ---       #185
      01002429H   LINE      CODE     ---       #186
      0100242AH   LINE      CODE     ---       #187
      0100242FH   LINE      CODE     ---       #188
      01002430H   LINE      CODE     ---       #189
      01002431H   LINE      CODE     ---       #190
      01002432H   LINE      CODE     ---       #191
      01002433H   LINE      CODE     ---       #192
      01002434H   LINE      CODE     ---       #193
      01002435H   LINE      CODE     ---       #194
      0100243AH   LINE      CODE     ---       #195
      0100243CH   LINE      CODE     ---       #196
      0100243DH   LINE      CODE     ---       #198
      0100243DH   LINE      CODE     ---       #199
      01002442H   LINE      CODE     ---       #200
      01002443H   LINE      CODE     ---       #201
      01002444H   LINE      CODE     ---       #202
      01002445H   LINE      CODE     ---       #203
      01002446H   LINE      CODE     ---       #204
      01002447H   LINE      CODE     ---       #205
      01002448H   LINE      CODE     ---       #206
      0100244DH   LINE      CODE     ---       #207
      0100244DH   LINE      CODE     ---       #208
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_INIT
      0100263EH   PUBLIC    CODE     ---       ADC_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 54


      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 55


      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 56


      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100263EH   BLOCK     CODE     ---       LVL=0
      0100263EH   LINE      CODE     ---       #65
      0100263EH   LINE      CODE     ---       #66
      0100263EH   LINE      CODE     ---       #68
      01002645H   LINE      CODE     ---       #71
      0100264AH   LINE      CODE     ---       #72
      01002650H   LINE      CODE     ---       #75
      01002655H   LINE      CODE     ---       #78
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       DEFINE
      020000B0H   PUBLIC    XDATA    BYTE      Motor_Direction_Data
      020000AEH   PUBLIC    XDATA    WORD      Power_Off_Wait_Time
      020000ACH   PUBLIC    XDATA    WORD      Battery_ADC_Wait_Time
      00000027H.1 PUBLIC    BIT      BIT       Delay_Over
      020000ABH   PUBLIC    XDATA    BYTE      Get_String_Wait_Time
      020000A9H   PUBLIC    XDATA    WORD      Delay_Time
      00000027H.0 PUBLIC    BIT      BIT       Get_String_Buff
      020000A7H   PUBLIC    XDATA    INT       Count_Toggle
      020000A5H   PUBLIC    XDATA    WORD      Delay_Time_Count
      020000A3H   PUBLIC    XDATA    WORD      Motor_Speed_Data
      020000A1H   PUBLIC    XDATA    INT       Num
      020000A0H   PUBLIC    XDATA    BYTE      K3_cnt
      0200009FH   PUBLIC    XDATA    BYTE      K2_cnt
      0200009EH   PUBLIC    XDATA    BYTE      K1_cnt
      0200009CH   PUBLIC    XDATA    WORD      longhit_cnt
      00000026H.7 PUBLIC    BIT      BIT       Center_Line_Control
      00000026H.6 PUBLIC    BIT      BIT       Power_count_clean
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 57


      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 58


      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 59


      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      ---         MODULE    ---      ---       GPIO_INIT
      010026A8H   PUBLIC    CODE     ---       GPIO_Key_Interrupt_Config
      0100201BH   PUBLIC    CODE     ---       GPIO_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 60


      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 61


      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 62


      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100201BH   BLOCK     CODE     ---       LVL=0
      0100201BH   LINE      CODE     ---       #42
      0100201BH   LINE      CODE     ---       #43
      0100201BH   LINE      CODE     ---       #44
      0100201EH   LINE      CODE     ---       #45
      01002021H   LINE      CODE     ---       #46
      01002024H   LINE      CODE     ---       #47
      01002027H   LINE      CODE     ---       #48
      0100202AH   LINE      CODE     ---       #49
      0100202DH   LINE      CODE     ---       #50
      01002030H   LINE      CODE     ---       #51
      01002033H   LINE      CODE     ---       #77
      01002038H   LINE      CODE     ---       #78
      0100203BH   LINE      CODE     ---       #79
      01002042H   LINE      CODE     ---       #81
      01002047H   LINE      CODE     ---       #82
      0100204AH   LINE      CODE     ---       #83
      01002051H   LINE      CODE     ---       #85
      01002056H   LINE      CODE     ---       #86
      01002059H   LINE      CODE     ---       #87
      01002060H   LINE      CODE     ---       #90
      01002065H   LINE      CODE     ---       #91
      01002068H   LINE      CODE     ---       #92
      0100206FH   LINE      CODE     ---       #94
      01002074H   LINE      CODE     ---       #95
      01002077H   LINE      CODE     ---       #96
      0100207EH   LINE      CODE     ---       #100
      01002083H   LINE      CODE     ---       #101
      01002086H   LINE      CODE     ---       #102
      01002088H   LINE      CODE     ---       #105
      0100208AH   LINE      CODE     ---       #122
      ---         BLOCKEND  ---      ---       LVL=0

LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 63


      010026A8H   BLOCK     CODE     ---       LVL=0
      010026A8H   LINE      CODE     ---       #131
      010026A8H   LINE      CODE     ---       #132
      010026A8H   LINE      CODE     ---       #134
      010026AEH   LINE      CODE     ---       #135
      010026B1H   LINE      CODE     ---       #138
      010026B5H   LINE      CODE     ---       #139
      010026B8H   LINE      CODE     ---       #142
      010026BAH   LINE      CODE     ---       #143
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       TIMER_INIT
      010024A5H   PUBLIC    CODE     ---       TMR1_Config
      0100247BH   PUBLIC    CODE     ---       TMR0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 64


      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 65


      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100247BH   BLOCK     CODE     ---       LVL=0
      0100247BH   LINE      CODE     ---       #11
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 66


      0100247BH   LINE      CODE     ---       #12
      0100247BH   LINE      CODE     ---       #16
      01002483H   LINE      CODE     ---       #20
      01002489H   LINE      CODE     ---       #24
      01002492H   LINE      CODE     ---       #29
      01002497H   LINE      CODE     ---       #34
      0100249DH   LINE      CODE     ---       #35
      010024A0H   LINE      CODE     ---       #40
      ---         BLOCKEND  ---      ---       LVL=0

      010024A5H   BLOCK     CODE     ---       LVL=0
      010024A5H   LINE      CODE     ---       #50
      010024A5H   LINE      CODE     ---       #51
      010024A5H   LINE      CODE     ---       #55
      010024AEH   LINE      CODE     ---       #59
      010024B5H   LINE      CODE     ---       #63
      010024BEH   LINE      CODE     ---       #68
      010024C3H   LINE      CODE     ---       #73
      010024C6H   LINE      CODE     ---       #74
      010024C9H   LINE      CODE     ---       #79
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_INIT
      0200001FH   PUBLIC    XDATA    BYTE      ?_UART_Send_String?BYTE
      01002157H   PUBLIC    CODE     ---       _UART_Send_String
      01000056H   PUBLIC    CODE     ---       _putchar
      0100233BH   PUBLIC    CODE     ---       UART_1_Config
      0100220FH   PUBLIC    CODE     ---       UART_0_Config
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 67


      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 68


      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 69


      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100220FH   BLOCK     CODE     ---       LVL=0
      0100220FH   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      BRTValue
      02000013H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      0100220FH   LINE      CODE     ---       #42
      0100220FH   LINE      CODE     ---       #43
      0100220FH   LINE      CODE     ---       #44
      01002219H   LINE      CODE     ---       #45
      01002221H   LINE      CODE     ---       #143
      0100222AH   LINE      CODE     ---       #144
      0100222FH   LINE      CODE     ---       #147
      01002234H   LINE      CODE     ---       #148
      01002239H   LINE      CODE     ---       #152
      01002244H   LINE      CODE     ---       #153
      01002247H   LINE      CODE     ---       #156
      0100224DH   LINE      CODE     ---       #157
      01002252H   LINE      CODE     ---       #159
      01002257H   LINE      CODE     ---       #160
      0100225AH   LINE      CODE     ---       #161
      0100225DH   LINE      CODE     ---       #162
      ---         BLOCKEND  ---      ---       LVL=0

      0100233BH   BLOCK     CODE     ---       LVL=0
      0100233BH   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      BRTValue
      02000013H   SYMBOL    XDATA    DWORD     BaudRateVlue
      ---         BLOCKEND  ---      ---       LVL=1
      0100233BH   LINE      CODE     ---       #223
      0100233BH   LINE      CODE     ---       #224
      0100233BH   LINE      CODE     ---       #225
      01002345H   LINE      CODE     ---       #226
      0100234DH   LINE      CODE     ---       #324
      01002356H   LINE      CODE     ---       #325
      0100235BH   LINE      CODE     ---       #328
      01002360H   LINE      CODE     ---       #329
      01002365H   LINE      CODE     ---       #333
      01002370H   LINE      CODE     ---       #334
      01002373H   LINE      CODE     ---       #337
      01002379H   LINE      CODE     ---       #347
      ---         BLOCKEND  ---      ---       LVL=0

      01000056H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     CHAR      ch
      01000056H   LINE      CODE     ---       #359
      01000056H   LINE      CODE     ---       #360
      01000056H   LINE      CODE     ---       #361
      01000058H   LINE      CODE     ---       #362
      0100005DH   LINE      CODE     ---       #363
      01000060H   LINE      CODE     ---       #364
      01000062H   LINE      CODE     ---       #365
      ---         BLOCKEND  ---      ---       LVL=0
      00000001H   SYMBOL    DATA     ---       s
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 70



      01002157H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      UARTn
      02000020H   SYMBOL    XDATA    ---       String
      02000023H   SYMBOL    XDATA    BYTE      Length
      01002162H   BLOCK     CODE     NEAR LAB  LVL=1
      00000006H   SYMBOL    DATA     BYTE      Index
      ---         BLOCKEND  ---      ---       LVL=1
      01002157H   LINE      CODE     ---       #408
      01002162H   LINE      CODE     ---       #409
      01002162H   LINE      CODE     ---       #410
      01002164H   LINE      CODE     ---       #411
      0100216EH   LINE      CODE     ---       #412
      0100216EH   LINE      CODE     ---       #413
      01002171H   LINE      CODE     ---       #414
      01002171H   LINE      CODE     ---       #415
      01002186H   LINE      CODE     ---       #416
      0100218BH   LINE      CODE     ---       #417
      0100218EH   LINE      CODE     ---       #418
      0100218EH   LINE      CODE     ---       #419
      01002193H   LINE      CODE     ---       #420
      01002193H   LINE      CODE     ---       #421
      010021A8H   LINE      CODE     ---       #422
      010021ADH   LINE      CODE     ---       #423
      010021B0H   LINE      CODE     ---       #424
      010021B0H   LINE      CODE     ---       #425
      010021B3H   LINE      CODE     ---       #426
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ISR
      020000C4H   PUBLIC    XDATA    INT       Num_Reverse_Pulse
      020000C2H   PUBLIC    XDATA    INT       Num_Forward_Pulse
      01000037H   PUBLIC    CODE     ---       SPI_IRQHandler
      01000036H   PUBLIC    CODE     ---       I2C_IRQHandler
      01000032H   PUBLIC    CODE     ---       WDT_IRQHandler
      01000031H   PUBLIC    CODE     ---       ADC_IRQHandler
      01000030H   PUBLIC    CODE     ---       EPWM_IRQHandler
      0100002FH   PUBLIC    CODE     ---       Timer4_IRQHandler
      0100002EH   PUBLIC    CODE     ---       Timer3_IRQHandler
      0100002AH   PUBLIC    CODE     ---       ACMP_IRQHandler
      01000029H   PUBLIC    CODE     ---       LSE_IRQHandler
      01000028H   PUBLIC    CODE     ---       LVD_IRQHandler
      01000027H   PUBLIC    CODE     ---       P3EI_IRQHandler
      01001F30H   PUBLIC    CODE     ---       P2EI_IRQHandler
      01001EB8H   PUBLIC    CODE     ---       P1EI_IRQHandler
      01000026H   PUBLIC    CODE     ---       P0EI_IRQHandler
      01000022H   PUBLIC    CODE     ---       UART1_IRQHandler
      0100001AH   PUBLIC    CODE     ---       Timer2_IRQHandler
      010021B4H   PUBLIC    CODE     ---       UART0_IRQHandler
      010026EFH   PUBLIC    CODE     ---       Timer1_IRQHandler
      01000012H   PUBLIC    CODE     ---       INT1_IRQHandler
      01001BADH   PUBLIC    CODE     ---       Timer0_IRQHandler
      0100000AH   PUBLIC    CODE     ---       INT0_IRQHandler
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 71


      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 72


      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 73


      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100000AH   BLOCK     CODE     ---       LVL=0
      0100000AH   LINE      CODE     ---       #70
      0100000AH   LINE      CODE     ---       #73
      ---         BLOCKEND  ---      ---       LVL=0

      01001BADH   BLOCK     CODE     ---       LVL=0
      01001BADH   LINE      CODE     ---       #82
      01001BBCH   LINE      CODE     ---       #84
      01001BBFH   LINE      CODE     ---       #85
      01001BC2H   LINE      CODE     ---       #87
      01001BC4H   LINE      CODE     ---       #88
      01001BD2H   LINE      CODE     ---       #89
      01001BE0H   LINE      CODE     ---       #90
      01001BF5H   LINE      CODE     ---       #92
      01001C0DH   LINE      CODE     ---       #93
      01001C0DH   LINE      CODE     ---       #94
      01001C0DH   LINE      CODE     ---       #95
      01001C0DH   LINE      CODE     ---       #96
      01001C19H   LINE      CODE     ---       #97
      01001C19H   LINE      CODE     ---       #98
      01001C19H   LINE      CODE     ---       #99
      01001C19H   LINE      CODE     ---       #100
      01001C19H   LINE      CODE     ---       #101
      01001C19H   LINE      CODE     ---       #102
      01001C1BH   LINE      CODE     ---       #103
      01001C1BH   LINE      CODE     ---       #104
      01001C27H   LINE      CODE     ---       #105
      01001C27H   LINE      CODE     ---       #106
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 74


      01001C27H   LINE      CODE     ---       #107
      01001C27H   LINE      CODE     ---       #108
      01001C27H   LINE      CODE     ---       #109
      01001C29H   LINE      CODE     ---       #110
      01001C29H   LINE      CODE     ---       #111
      01001C29H   LINE      CODE     ---       #112
      01001C35H   LINE      CODE     ---       #113
      01001C35H   LINE      CODE     ---       #114
      01001C37H   LINE      CODE     ---       #115
      01001C3DH   LINE      CODE     ---       #116
      01001C3DH   LINE      CODE     ---       #117
      01001C3DH   LINE      CODE     ---       #118
      01001C3DH   LINE      CODE     ---       #119
      01001C3DH   LINE      CODE     ---       #120
      01001C3DH   LINE      CODE     ---       #121
      01001C3DH   LINE      CODE     ---       #124
      01001C40H   LINE      CODE     ---       #125
      01001C40H   LINE      CODE     ---       #126
      01001C49H   LINE      CODE     ---       #127
      01001C4BH   LINE      CODE     ---       #129
      01001C4BH   LINE      CODE     ---       #130
      01001C50H   LINE      CODE     ---       #131
      01001C50H   LINE      CODE     ---       #134
      01001C5DH   LINE      CODE     ---       #135
      01001C5DH   LINE      CODE     ---       #136
      01001C5FH   LINE      CODE     ---       #137
      01001C6DH   LINE      CODE     ---       #138
      01001C6FH   LINE      CODE     ---       #140
      01001C6FH   LINE      CODE     ---       #141
      01001C71H   LINE      CODE     ---       #142
      01001C78H   LINE      CODE     ---       #143
      01001C78H   LINE      CODE     ---       #146
      01001C7BH   LINE      CODE     ---       #147
      01001C7BH   LINE      CODE     ---       #148
      01001C89H   LINE      CODE     ---       #149
      01001C9EH   LINE      CODE     ---       #150
      01001C9EH   LINE      CODE     ---       #151
      01001CA0H   LINE      CODE     ---       #152
      01001CA0H   LINE      CODE     ---       #153
      01001CA0H   LINE      CODE     ---       #154
      01001CA2H   LINE      CODE     ---       #156
      01001CA2H   LINE      CODE     ---       #157
      01001CA9H   LINE      CODE     ---       #158
      01001CA9H   LINE      CODE     ---       #159
      ---         BLOCKEND  ---      ---       LVL=0

      01000012H   BLOCK     CODE     ---       LVL=0
      01000012H   LINE      CODE     ---       #168
      01000012H   LINE      CODE     ---       #171
      ---         BLOCKEND  ---      ---       LVL=0

      010026EFH   BLOCK     CODE     ---       LVL=0
      010026EFH   LINE      CODE     ---       #180
      010026EFH   LINE      CODE     ---       #182
      010026F2H   LINE      CODE     ---       #183
      010026F5H   LINE      CODE     ---       #199
      ---         BLOCKEND  ---      ---       LVL=0

      010021B4H   BLOCK     CODE     ---       LVL=0
      010021B4H   LINE      CODE     ---       #208
      010021D1H   LINE      CODE     ---       #210
      010021D9H   LINE      CODE     ---       #211
      010021D9H   LINE      CODE     ---       #212
      010021DBH   LINE      CODE     ---       #213
      010021E0H   LINE      CODE     ---       #214
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 75


      010021EFH   LINE      CODE     ---       #215
      010021F4H   LINE      CODE     ---       #216
      010021F4H   LINE      CODE     ---       #217
      ---         BLOCKEND  ---      ---       LVL=0

      0100001AH   BLOCK     CODE     ---       LVL=0
      0100001AH   LINE      CODE     ---       #226
      0100001AH   LINE      CODE     ---       #229
      ---         BLOCKEND  ---      ---       LVL=0

      01000022H   BLOCK     CODE     ---       LVL=0
      01000022H   LINE      CODE     ---       #238
      01000022H   LINE      CODE     ---       #241
      ---         BLOCKEND  ---      ---       LVL=0

      01000026H   BLOCK     CODE     ---       LVL=0
      01000026H   LINE      CODE     ---       #250
      01000026H   LINE      CODE     ---       #253
      ---         BLOCKEND  ---      ---       LVL=0

      01001EB8H   BLOCK     CODE     ---       LVL=0
      01001EB8H   LINE      CODE     ---       #262
      01001EC7H   LINE      CODE     ---       #265
      01001ECAH   LINE      CODE     ---       #267
      01001ECDH   LINE      CODE     ---       #268
      01001ECDH   LINE      CODE     ---       #270
      01001ED0H   LINE      CODE     ---       #271
      01001ED0H   LINE      CODE     ---       #272
      01001EDFH   LINE      CODE     ---       #273
      01001EE1H   LINE      CODE     ---       #274
      01001EE3H   LINE      CODE     ---       #275
      01001EE3H   LINE      CODE     ---       #276
      01001EE5H   LINE      CODE     ---       #278
      01001EE5H   LINE      CODE     ---       #279
      01001EE8H   LINE      CODE     ---       #280
      01001EE8H   LINE      CODE     ---       #281
      01001F03H   LINE      CODE     ---       #282
      01001F1FH   LINE      CODE     ---       #283
      01001F1FH   LINE      CODE     ---       #284
      01001F21H   LINE      CODE     ---       #285
      01001F21H   LINE      CODE     ---       #286
      01001F23H   LINE      CODE     ---       #287
      01001F23H   LINE      CODE     ---       #288
      01001F23H   LINE      CODE     ---       #289
      ---         BLOCKEND  ---      ---       LVL=0

      01001F30H   BLOCK     CODE     ---       LVL=0
      01001F30H   LINE      CODE     ---       #298
      01001F3FH   LINE      CODE     ---       #301
      01001F42H   LINE      CODE     ---       #303
      01001F45H   LINE      CODE     ---       #304
      01001F45H   LINE      CODE     ---       #306
      01001F48H   LINE      CODE     ---       #307
      01001F48H   LINE      CODE     ---       #308
      01001F57H   LINE      CODE     ---       #309
      01001F59H   LINE      CODE     ---       #310
      01001F5BH   LINE      CODE     ---       #311
      01001F5BH   LINE      CODE     ---       #312
      01001F5DH   LINE      CODE     ---       #314
      01001F5DH   LINE      CODE     ---       #315
      01001F60H   LINE      CODE     ---       #316
      01001F60H   LINE      CODE     ---       #317
      01001F7BH   LINE      CODE     ---       #318
      01001F97H   LINE      CODE     ---       #319
      01001F97H   LINE      CODE     ---       #320
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 76


      01001F99H   LINE      CODE     ---       #321
      01001F99H   LINE      CODE     ---       #322
      01001F9BH   LINE      CODE     ---       #323
      01001F9BH   LINE      CODE     ---       #324
      01001F9BH   LINE      CODE     ---       #325
      ---         BLOCKEND  ---      ---       LVL=0

      01000027H   BLOCK     CODE     ---       LVL=0
      01000027H   LINE      CODE     ---       #334
      01000027H   LINE      CODE     ---       #337
      ---         BLOCKEND  ---      ---       LVL=0

      01000028H   BLOCK     CODE     ---       LVL=0
      01000028H   LINE      CODE     ---       #346
      01000028H   LINE      CODE     ---       #349
      ---         BLOCKEND  ---      ---       LVL=0

      01000029H   BLOCK     CODE     ---       LVL=0
      01000029H   LINE      CODE     ---       #358
      01000029H   LINE      CODE     ---       #361
      ---         BLOCKEND  ---      ---       LVL=0

      0100002AH   BLOCK     CODE     ---       LVL=0
      0100002AH   LINE      CODE     ---       #370
      0100002AH   LINE      CODE     ---       #373
      ---         BLOCKEND  ---      ---       LVL=0

      0100002EH   BLOCK     CODE     ---       LVL=0
      0100002EH   LINE      CODE     ---       #382
      0100002EH   LINE      CODE     ---       #385
      ---         BLOCKEND  ---      ---       LVL=0

      0100002FH   BLOCK     CODE     ---       LVL=0
      0100002FH   LINE      CODE     ---       #394
      0100002FH   LINE      CODE     ---       #397
      ---         BLOCKEND  ---      ---       LVL=0

      01000030H   BLOCK     CODE     ---       LVL=0
      01000030H   LINE      CODE     ---       #406
      01000030H   LINE      CODE     ---       #409
      ---         BLOCKEND  ---      ---       LVL=0

      01000031H   BLOCK     CODE     ---       LVL=0
      01000031H   LINE      CODE     ---       #418
      01000031H   LINE      CODE     ---       #421
      ---         BLOCKEND  ---      ---       LVL=0

      01000032H   BLOCK     CODE     ---       LVL=0
      01000032H   LINE      CODE     ---       #430
      01000032H   LINE      CODE     ---       #433
      ---         BLOCKEND  ---      ---       LVL=0

      01000036H   BLOCK     CODE     ---       LVL=0
      01000036H   LINE      CODE     ---       #442
      01000036H   LINE      CODE     ---       #445
      ---         BLOCKEND  ---      ---       LVL=0

      01000037H   BLOCK     CODE     ---       LVL=0
      01000037H   LINE      CODE     ---       #454
      01000037H   LINE      CODE     ---       #457
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       UART_FUNCTION
      0200009BH   PUBLIC    XDATA    BYTE      Data_Length
      0200007BH   PUBLIC    XDATA    ---       UART_Get_String
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 77


      01001984H   PUBLIC    CODE     ---       UART_Data_Process
      01001FA8H   PUBLIC    CODE     ---       _Function_UART_Send_CMD
      01002585H   PUBLIC    CODE     ---       UART_Data_Init
      010026FCH   PUBLIC    CODE     ---       Clean_UART_Data_Length
      010026F6H   PUBLIC    CODE     ---       Return_UART_Data_Length
      01002565H   PUBLIC    CODE     ---       _UART_Data_Copy
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 78


      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 79


      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON
      0100271DH   SYMBOL    CONST    ---       _?ix1000
      01002720H   SYMBOL    CONST    ---       _?ix1001
      01002723H   SYMBOL    CONST    ---       _?ix1002

      01002565H   BLOCK     CODE     ---       LVL=0
      00000001H   SYMBOL    DATA     ---       Data_Point
      00000005H   SYMBOL    DATA     BYTE      Source_Data
      01002565H   LINE      CODE     ---       #12
      01002565H   LINE      CODE     ---       #13
      01002565H   LINE      CODE     ---       #14
      01002572H   LINE      CODE     ---       #15
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 80


      01002578H   LINE      CODE     ---       #16
      01002582H   LINE      CODE     ---       #17
      01002582H   LINE      CODE     ---       #18
      01002584H   LINE      CODE     ---       #19
      01002584H   LINE      CODE     ---       #20
      ---         BLOCKEND  ---      ---       LVL=0

      010026F6H   BLOCK     CODE     ---       LVL=0
      010026F6H   LINE      CODE     ---       #24
      010026F6H   LINE      CODE     ---       #25
      010026F6H   LINE      CODE     ---       #26
      010026FBH   LINE      CODE     ---       #27
      ---         BLOCKEND  ---      ---       LVL=0

      010026FCH   BLOCK     CODE     ---       LVL=0
      010026FCH   LINE      CODE     ---       #30
      010026FCH   LINE      CODE     ---       #31
      010026FCH   LINE      CODE     ---       #32
      01002701H   LINE      CODE     ---       #33
      ---         BLOCKEND  ---      ---       LVL=0

      01002585H   BLOCK     CODE     ---       LVL=0
      01002585H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      ---         BLOCKEND  ---      ---       LVL=1
      01002585H   LINE      CODE     ---       #36
      01002585H   LINE      CODE     ---       #37
      01002585H   LINE      CODE     ---       #39
      0100258AH   LINE      CODE     ---       #40
      01002595H   LINE      CODE     ---       #41
      01002595H   LINE      CODE     ---       #42
      010025A1H   LINE      CODE     ---       #43
      010025A4H   LINE      CODE     ---       #44
      ---         BLOCKEND  ---      ---       LVL=0

      01001FA8H   BLOCK     CODE     ---       LVL=0
      02000011H   SYMBOL    XDATA    BYTE      CMD_No
      01001FADH   BLOCK     CODE     NEAR LAB  LVL=1
      02000012H   SYMBOL    XDATA    ---       UART_PASS_Data
      02000015H   SYMBOL    XDATA    ---       UART_Error_Data
      02000018H   SYMBOL    XDATA    ---       UART_Clean_Pair
      ---         BLOCKEND  ---      ---       LVL=1
      01001FA8H   LINE      CODE     ---       #61
      01001FADH   LINE      CODE     ---       #62
      01001FADH   LINE      CODE     ---       #63
      01001FC0H   LINE      CODE     ---       #64
      01001FD3H   LINE      CODE     ---       #65
      01001FE6H   LINE      CODE     ---       #66
      01001FF4H   LINE      CODE     ---       #67
      01001FF4H   LINE      CODE     ---       #69
      01001FF4H   LINE      CODE     ---       #70
      01001FF4H   LINE      CODE     ---       #71
      01001FFAH   LINE      CODE     ---       #72
      01001FFAH   LINE      CODE     ---       #73
      01001FFCH   LINE      CODE     ---       #75
      01001FFCH   LINE      CODE     ---       #76
      01001FFCH   LINE      CODE     ---       #77
      01002007H   LINE      CODE     ---       #78
      01002007H   LINE      CODE     ---       #79
      01002009H   LINE      CODE     ---       #81
      01002009H   LINE      CODE     ---       #82
      01002009H   LINE      CODE     ---       #83
      0100201AH   LINE      CODE     ---       #86
      0100201AH   LINE      CODE     ---       #87
      0100201AH   LINE      CODE     ---       #88
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 81


      0100201AH   LINE      CODE     ---       #89
      ---         BLOCKEND  ---      ---       LVL=0

      01001984H   BLOCK     CODE     ---       LVL=0
      01001984H   BLOCK     CODE     NEAR LAB  LVL=1
      00000007H   SYMBOL    DATA     BYTE      i
      00000006H   SYMBOL    DATA     BYTE      Return_Data
      ---         BLOCKEND  ---      ---       LVL=1
      01001984H   LINE      CODE     ---       #92
      01001984H   LINE      CODE     ---       #93
      01001984H   LINE      CODE     ---       #96
      01001987H   LINE      CODE     ---       #98
      0100198CH   LINE      CODE     ---       #99
      0100198CH   LINE      CODE     ---       #100
      0100198FH   LINE      CODE     ---       #101
      01001992H   LINE      CODE     ---       #102
      01001992H   LINE      CODE     ---       #104
      0100199DH   LINE      CODE     ---       #105
      0100199DH   LINE      CODE     ---       #106
      010019ACH   LINE      CODE     ---       #107
      010019ACH   LINE      CODE     ---       #109
      010019AEH   LINE      CODE     ---       #110
      010019B1H   LINE      CODE     ---       #111
      010019BFH   LINE      CODE     ---       #112
      010019BFH   LINE      CODE     ---       #114
      010019C1H   LINE      CODE     ---       #115
      010019C4H   LINE      CODE     ---       #116
      010019D2H   LINE      CODE     ---       #117
      010019D2H   LINE      CODE     ---       #119
      010019D4H   LINE      CODE     ---       #120
      010019D7H   LINE      CODE     ---       #121
      010019E8H   LINE      CODE     ---       #122
      010019E8H   LINE      CODE     ---       #124
      010019EAH   LINE      CODE     ---       #125
      010019EDH   LINE      CODE     ---       #126
      010019FBH   LINE      CODE     ---       #127
      010019FBH   LINE      CODE     ---       #129
      010019FDH   LINE      CODE     ---       #130
      01001A00H   LINE      CODE     ---       #131
      01001A0EH   LINE      CODE     ---       #132
      01001A0EH   LINE      CODE     ---       #134
      01001A10H   LINE      CODE     ---       #135
      01001A13H   LINE      CODE     ---       #136
      01001A24H   LINE      CODE     ---       #137
      01001A24H   LINE      CODE     ---       #139
      01001A26H   LINE      CODE     ---       #140
      01001A28H   LINE      CODE     ---       #141
      01001A36H   LINE      CODE     ---       #142
      01001A36H   LINE      CODE     ---       #144
      01001A38H   LINE      CODE     ---       #145
      01001A3AH   LINE      CODE     ---       #146
      01001A48H   LINE      CODE     ---       #147
      01001A48H   LINE      CODE     ---       #149
      01001A4AH   LINE      CODE     ---       #150
      01001A4CH   LINE      CODE     ---       #151
      01001A5DH   LINE      CODE     ---       #152
      01001A5DH   LINE      CODE     ---       #154
      01001A5FH   LINE      CODE     ---       #155
      01001A61H   LINE      CODE     ---       #156
      01001A6FH   LINE      CODE     ---       #157
      01001A6FH   LINE      CODE     ---       #159
      01001A71H   LINE      CODE     ---       #160
      01001A73H   LINE      CODE     ---       #161
      01001A81H   LINE      CODE     ---       #162
      01001A81H   LINE      CODE     ---       #164
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 82


      01001A83H   LINE      CODE     ---       #165
      01001A85H   LINE      CODE     ---       #166
      01001A91H   LINE      CODE     ---       #167
      01001A91H   LINE      CODE     ---       #169
      01001A93H   LINE      CODE     ---       #170
      01001A95H   LINE      CODE     ---       #172
      01001A95H   LINE      CODE     ---       #174
      01001A97H   LINE      CODE     ---       #175
      01001A97H   LINE      CODE     ---       #176
      01001A97H   LINE      CODE     ---       #178
      01001A9AH   LINE      CODE     ---       #180
      01001A9CH   LINE      CODE     ---       #181
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       BATTERY_FUNCTION

      ---         MODULE    ---      ---       KEY
      00000026H.5 PUBLIC    BIT      BIT       K5_Press
      00000026H.4 PUBLIC    BIT      BIT       K4_Press
      00000026H.3 PUBLIC    BIT      BIT       K3_Press
      00000026H.2 PUBLIC    BIT      BIT       K2_Press
      00000026H.1 PUBLIC    BIT      BIT       K1_Press
      020000C1H   PUBLIC    XDATA    BYTE      K5_Count
      020000C0H   PUBLIC    XDATA    BYTE      K4_Count
      020000BFH   PUBLIC    XDATA    BYTE      K3_Count
      020000BEH   PUBLIC    XDATA    BYTE      K2_Count
      020000BDH   PUBLIC    XDATA    BYTE      K1_Count
      020000BCH   PUBLIC    XDATA    BYTE      Key_Buff
      0100237AH   PUBLIC    CODE     ---       Key_Buff_Return
      0100185AH   PUBLIC    CODE     ---       Key_Scan
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 83


      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 84


      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 85


      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100185AH   BLOCK     CODE     ---       LVL=0
      0100185AH   LINE      CODE     ---       #20
      0100185AH   LINE      CODE     ---       #21
      0100185AH   LINE      CODE     ---       #23
      0100185DH   LINE      CODE     ---       #24
      0100185DH   LINE      CODE     ---       #25
      01001860H   LINE      CODE     ---       #26
      01001860H   LINE      CODE     ---       #27
      0100186DH   LINE      CODE     ---       #28
      0100186DH   LINE      CODE     ---       #29
      0100186FH   LINE      CODE     ---       #30
      01001871H   LINE      CODE     ---       #31
      01001873H   LINE      CODE     ---       #43
      01001873H   LINE      CODE     ---       #44
      01001876H   LINE      CODE     ---       #45
      01001876H   LINE      CODE     ---       #46
      01001883H   LINE      CODE     ---       #47
      01001883H   LINE      CODE     ---       #48
      01001885H   LINE      CODE     ---       #49
      01001887H   LINE      CODE     ---       #50
      01001889H   LINE      CODE     ---       #52
      01001889H   LINE      CODE     ---       #53
      0100188FH   LINE      CODE     ---       #54
      0100188FH   LINE      CODE     ---       #55
      01001891H   LINE      CODE     ---       #57
      01001891H   LINE      CODE     ---       #58
      01001896H   LINE      CODE     ---       #59
      01001896H   LINE      CODE     ---       #60
      01001896H   LINE      CODE     ---       #63
      01001899H   LINE      CODE     ---       #64
      01001899H   LINE      CODE     ---       #65
      0100189CH   LINE      CODE     ---       #66
      0100189CH   LINE      CODE     ---       #67
      010018A9H   LINE      CODE     ---       #68
      010018A9H   LINE      CODE     ---       #69
      010018ABH   LINE      CODE     ---       #70
      010018ADH   LINE      CODE     ---       #71
      010018AFH   LINE      CODE     ---       #83
      010018AFH   LINE      CODE     ---       #84
      010018B2H   LINE      CODE     ---       #85
      010018B2H   LINE      CODE     ---       #86
      010018BFH   LINE      CODE     ---       #87
      010018BFH   LINE      CODE     ---       #88
      010018C1H   LINE      CODE     ---       #89
      010018C3H   LINE      CODE     ---       #90
      010018C5H   LINE      CODE     ---       #92
      010018C5H   LINE      CODE     ---       #93
      010018CBH   LINE      CODE     ---       #94
      010018CBH   LINE      CODE     ---       #95
      010018CDH   LINE      CODE     ---       #97
      010018CDH   LINE      CODE     ---       #98
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 86


      010018D2H   LINE      CODE     ---       #99
      010018D2H   LINE      CODE     ---       #100
      010018D2H   LINE      CODE     ---       #103
      010018D5H   LINE      CODE     ---       #104
      010018D5H   LINE      CODE     ---       #105
      010018D8H   LINE      CODE     ---       #106
      010018D8H   LINE      CODE     ---       #107
      010018E5H   LINE      CODE     ---       #108
      010018E5H   LINE      CODE     ---       #109
      010018E7H   LINE      CODE     ---       #110
      010018E9H   LINE      CODE     ---       #111
      010018EBH   LINE      CODE     ---       #123
      010018EBH   LINE      CODE     ---       #124
      010018EEH   LINE      CODE     ---       #125
      010018EEH   LINE      CODE     ---       #126
      010018FBH   LINE      CODE     ---       #127
      010018FBH   LINE      CODE     ---       #128
      010018FDH   LINE      CODE     ---       #129
      010018FFH   LINE      CODE     ---       #130
      01001901H   LINE      CODE     ---       #132
      01001901H   LINE      CODE     ---       #133
      01001907H   LINE      CODE     ---       #134
      01001907H   LINE      CODE     ---       #135
      01001909H   LINE      CODE     ---       #137
      01001909H   LINE      CODE     ---       #138
      0100190EH   LINE      CODE     ---       #139
      0100190EH   LINE      CODE     ---       #140
      0100190EH   LINE      CODE     ---       #143
      01001911H   LINE      CODE     ---       #144
      01001911H   LINE      CODE     ---       #145
      01001914H   LINE      CODE     ---       #146
      01001914H   LINE      CODE     ---       #147
      01001921H   LINE      CODE     ---       #148
      01001921H   LINE      CODE     ---       #149
      01001923H   LINE      CODE     ---       #150
      01001925H   LINE      CODE     ---       #151
      01001927H   LINE      CODE     ---       #163
      01001927H   LINE      CODE     ---       #164
      0100192AH   LINE      CODE     ---       #165
      0100192AH   LINE      CODE     ---       #166
      01001937H   LINE      CODE     ---       #167
      01001937H   LINE      CODE     ---       #168
      01001939H   LINE      CODE     ---       #169
      0100193BH   LINE      CODE     ---       #170
      0100193DH   LINE      CODE     ---       #172
      0100193DH   LINE      CODE     ---       #173
      01001943H   LINE      CODE     ---       #174
      01001943H   LINE      CODE     ---       #175
      01001945H   LINE      CODE     ---       #177
      01001945H   LINE      CODE     ---       #178
      0100194AH   LINE      CODE     ---       #179
      0100194AH   LINE      CODE     ---       #180
      0100194AH   LINE      CODE     ---       #183
      0100194DH   LINE      CODE     ---       #184
      0100194DH   LINE      CODE     ---       #185
      01001950H   LINE      CODE     ---       #186
      01001950H   LINE      CODE     ---       #187
      0100195DH   LINE      CODE     ---       #188
      0100195DH   LINE      CODE     ---       #189
      0100195FH   LINE      CODE     ---       #190
      01001961H   LINE      CODE     ---       #191
      01001962H   LINE      CODE     ---       #203
      01001962H   LINE      CODE     ---       #204
      01001965H   LINE      CODE     ---       #205
      01001965H   LINE      CODE     ---       #206
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 87


      01001972H   LINE      CODE     ---       #207
      01001972H   LINE      CODE     ---       #208
      01001974H   LINE      CODE     ---       #209
      01001976H   LINE      CODE     ---       #210
      01001977H   LINE      CODE     ---       #212
      01001977H   LINE      CODE     ---       #213
      0100197DH   LINE      CODE     ---       #214
      0100197DH   LINE      CODE     ---       #215
      0100197EH   LINE      CODE     ---       #217
      0100197EH   LINE      CODE     ---       #218
      01001983H   LINE      CODE     ---       #219
      01001983H   LINE      CODE     ---       #220
      01001983H   LINE      CODE     ---       #221
      ---         BLOCKEND  ---      ---       LVL=0

      0100237AH   BLOCK     CODE     ---       LVL=0
      0100237AH   LINE      CODE     ---       #224
      0100237AH   LINE      CODE     ---       #225
      0100237AH   LINE      CODE     ---       #226
      0100237FH   LINE      CODE     ---       #228
      01002386H   LINE      CODE     ---       #229
      01002390H   LINE      CODE     ---       #230
      0100239AH   LINE      CODE     ---       #231
      010023A4H   LINE      CODE     ---       #232
      010023AEH   LINE      CODE     ---       #234
      010023B3H   LINE      CODE     ---       #235
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ADC_USED
      0100171FH   PUBLIC    CODE     ---       _ADC
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 88


      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 89


      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 90


      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      0100171FH   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      ADC_Channel
      0100171FH   BLOCK     CODE     NEAR LAB  LVL=1
      020000B1H   SYMBOL    XDATA    ---       filter_buffer
      020000BBH   SYMBOL    XDATA    BYTE      filter_index
      02000011H   SYMBOL    XDATA    DWORD     sum
      02000015H   SYMBOL    XDATA    WORD      adc_result
      02000017H   SYMBOL    XDATA    BYTE      i
      02000018H   SYMBOL    XDATA    FLOAT     v_adc
      0200001CH   SYMBOL    XDATA    FLOAT     v_bat
      ---         BLOCKEND  ---      ---       LVL=1
      0100171FH   LINE      CODE     ---       #70
      0100171FH   LINE      CODE     ---       #71
      0100171FH   LINE      CODE     ---       #75
      01001729H   LINE      CODE     ---       #81
      0100172BH   LINE      CODE     ---       #82
      0100172EH   LINE      CODE     ---       #84
      01001731H   LINE      CODE     ---       #85
      01001736H   LINE      CODE     ---       #86
      01001741H   LINE      CODE     ---       #89
      01001756H   LINE      CODE     ---       #90
      01001769H   LINE      CODE     ---       #91
      0100177BH   LINE      CODE     ---       #92
      0100177BH   LINE      CODE     ---       #93
      010017B2H   LINE      CODE     ---       #94
      010017BAH   LINE      CODE     ---       #95
      010017D9H   LINE      CODE     ---       #98
      010017FCH   LINE      CODE     ---       #99
      01001825H   LINE      CODE     ---       #101
      01001851H   LINE      CODE     ---       #102
      01001859H   LINE      CODE     ---       #103
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       MAIN
      00000024H.3 PUBLIC    BIT      BIT       speedup
      00000024H.2 PUBLIC    BIT      BIT       longhit
      0200007AH   PUBLIC    XDATA    BYTE      ledonoff_cnt
      00000024H.1 PUBLIC    BIT      BIT       key_short_press_mode
      00000024H.0 PUBLIC    BIT      BIT       direction_changed
      00000023H.7 PUBLIC    BIT      BIT       Bit_N_ms_Buff
      02000078H   PUBLIC    XDATA    WORD      BatV
      00000023H.6 PUBLIC    BIT      BIT       key3_long_started
      00000023H.5 PUBLIC    BIT      BIT       key1_long_started
      00000023H.4 PUBLIC    BIT      BIT       k2_long_press_detected
      00000023H.3 PUBLIC    BIT      BIT       Bit_Toggle
      00000023H.2 PUBLIC    BIT      BIT       Delay_Open
      02000077H   PUBLIC    XDATA    BYTE      batlow1_cnt
      00000023H.1 PUBLIC    BIT      BIT       charge_flash
      02000076H   PUBLIC    XDATA    BYTE      last_direction
      00000023H.0 PUBLIC    BIT      BIT       key3_pressed
      00000022H.7 PUBLIC    BIT      BIT       Bit_1_ms_Buff
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 91


      00000022H.6 PUBLIC    BIT      BIT       key1_pressed
      02000075H   PUBLIC    XDATA    BYTE      System_Mode_Before_Charge
      00000022H.5 PUBLIC    BIT      BIT       Charge_Was_Connected
      00000022H.4 PUBLIC    BIT      BIT       ledonoff
      02000073H   PUBLIC    XDATA    WORD      original_speed
      00000022H.3 PUBLIC    BIT      BIT       key3_handle
      02000072H   PUBLIC    XDATA    BYTE      System_Mode_Data
      02000070H   PUBLIC    XDATA    INT       Self_Check
      00000022H.2 PUBLIC    BIT      BIT       key1_handle
      0200006EH   PUBLIC    XDATA    WORD      key3_duration
      0200006CH   PUBLIC    XDATA    WORD      dly
      00000022H.1 PUBLIC    BIT      BIT       k3_released
      0200006AH   PUBLIC    XDATA    WORD      key1_duration
      00000022H.0 PUBLIC    BIT      BIT       k2_released
      02000068H   PUBLIC    XDATA    WORD      k2_long_press_timer
      00000021H.7 PUBLIC    BIT      BIT       batlow1
      02000066H   PUBLIC    XDATA    INT       Count_1_Degree_Pulse
      00000021H.6 PUBLIC    BIT      BIT       auto_rotate_mode
      02000065H   PUBLIC    XDATA    BYTE      batlow_cnt
      00000021H.5 PUBLIC    BIT      BIT       Charg_State_Buff
      00000021H.4 PUBLIC    BIT      BIT       led_flash_state
      02000063H   PUBLIC    XDATA    WORD      led_flash_timer
      02000062H   PUBLIC    XDATA    BYTE      ledonoff1_cnt
      00000021H.3 PUBLIC    BIT      BIT       need_led_flash
      02000060H   PUBLIC    XDATA    WORD      auto_rotate_flash_timer
      0200005EH   PUBLIC    XDATA    WORD      timer_1ms_count
      0200005CH   PUBLIC    XDATA    WORD      speedup_cnt
      0200005AH   PUBLIC    XDATA    WORD      key3_press_time
      00000021H.2 PUBLIC    BIT      BIT       auto_rotate_flash
      02000058H   PUBLIC    XDATA    WORD      key1_press_time
      00000021H.1 PUBLIC    BIT      BIT       K3_cnt_EN
      02000054H   PUBLIC    XDATA    DWORD     Systemclock
      00000021H.0 PUBLIC    BIT      BIT       K2_cnt_EN
      00000020H.7 PUBLIC    BIT      BIT       K1_cnt_EN
      00000020H.6 PUBLIC    BIT      BIT       auto_rotate_running
      00000020H.5 PUBLIC    BIT      BIT       MOTOR_RUNNING_FLAG
      00000020H.4 PUBLIC    BIT      BIT       key_control_active
      00000020H.3 PUBLIC    BIT      BIT       Key_Long_Press
      00000020H.2 PUBLIC    BIT      BIT       batlow
      02000052H   PUBLIC    XDATA    WORD      charge_flash_cnt
      00000020H.1 PUBLIC    BIT      BIT       auto_rotate_entry_complete
      00000020H.0 PUBLIC    BIT      BIT       ledonoff1
      010015B4H   PUBLIC    CODE     ---       Key_Interrupt_Process
      01001A9DH   PUBLIC    CODE     ---       LED_Control
      010024F5H   PUBLIC    CODE     ---       Restore_dly
      0100244EH   PUBLIC    CODE     ---       _Store_dly
      0100208BH   PUBLIC    CODE     ---       Battery_Check
      010022F4H   PUBLIC    CODE     ---       _Key_Function_Switch_System
      01001E25H   PUBLIC    CODE     ---       _Motor_Step_Control
      010025A5H   PUBLIC    CODE     ---       _Delay1ms
      010000B6H   PUBLIC    CODE     ---       main
      000000C8H.0 SFRSYM    DATA     BIT       T2I0
      000000C3H   SFRSYM    DATA     BYTE      CCH1
      00000080H   SFRSYM    DATA     BYTE      P0
      000000C8H.1 SFRSYM    DATA     BIT       T2I1
      000000C5H   SFRSYM    DATA     BYTE      CCH2
      00000090H   SFRSYM    DATA     BYTE      P1
      000000C7H   SFRSYM    DATA     BYTE      CCH3
      000000ACH   SFRSYM    DATA     BYTE      P0EXTIE
      000000A0H   SFRSYM    DATA     BYTE      P2
      000000B4H   SFRSYM    DATA     BYTE      P0EXTIF
      000000B0H   SFRSYM    DATA     BYTE      P3
      000000ADH   SFRSYM    DATA     BYTE      P1EXTIE
      000000D0H.6 SFRSYM    DATA     BIT       AC
      000000FFH   SFRSYM    DATA     BYTE      MCTRL
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 92


      000000C2H   SFRSYM    DATA     BYTE      CCL1
      000000B5H   SFRSYM    DATA     BYTE      P1EXTIF
      000000AEH   SFRSYM    DATA     BYTE      P2EXTIE
      000000C4H   SFRSYM    DATA     BYTE      CCL2
      000000B6H   SFRSYM    DATA     BYTE      P2EXTIF
      000000AFH   SFRSYM    DATA     BYTE      P3EXTIE
      000000A8H.7 SFRSYM    DATA     BIT       EA
      000000C6H   SFRSYM    DATA     BYTE      CCL3
      000000B7H   SFRSYM    DATA     BYTE      P3EXTIF
      000000AAH   SFRSYM    DATA     BYTE      EIE2
      000000B2H   SFRSYM    DATA     BYTE      EIF2
      000000C8H.3 SFRSYM    DATA     BIT       T2R0
      000000C8H.4 SFRSYM    DATA     BIT       T2R1
      00000083H   SFRSYM    DATA     BYTE      DPH0
      000000A8H   SFRSYM    DATA     BYTE      IE
      00000085H   SFRSYM    DATA     BYTE      DPH1
      000000B9H   SFRSYM    DATA     BYTE      EIP1
      000000BAH   SFRSYM    DATA     BYTE      EIP2
      00000082H   SFRSYM    DATA     BYTE      DPL0
      00000084H   SFRSYM    DATA     BYTE      DPL1
      000000C8H.6 SFRSYM    DATA     BIT       I3FR
      000000CFH   SFRSYM    DATA     BYTE      T2IE
      00000096H   SFRSYM    DATA     BYTE      TA
      000000C9H   SFRSYM    DATA     BYTE      T2IF
      000000C8H.2 SFRSYM    DATA     BIT       T2CM
      000000DFH   SFRSYM    DATA     BYTE      ADCON0
      000000DEH   SFRSYM    DATA     BYTE      ADCON1
      000000E9H   SFRSYM    DATA     BYTE      ADCON2
      000000B8H   SFRSYM    DATA     BYTE      IP
      000000CEH   SFRSYM    DATA     BYTE      CCEN
      000000D0H.7 SFRSYM    DATA     BIT       CY
      000000D2H   SFRSYM    DATA     BYTE      T34MOD
      00000093H   SFRSYM    DATA     BYTE      DPX0
      00000095H   SFRSYM    DATA     BYTE      DPX1
      000000F4H   SFRSYM    DATA     BYTE      I2CMSA
      000000F5H   SFRSYM    DATA     BYTE      I2CMCR
      00000081H   SFRSYM    DATA     BYTE      SP
      000000D0H.2 SFRSYM    DATA     BIT       OV
      000000F2H   SFRSYM    DATA     BYTE      I2CSCR
      000000C8H.7 SFRSYM    DATA     BIT       T2PS
      000000D1H   SFRSYM    DATA     BYTE      ADCMPC
      000000CBH   SFRSYM    DATA     BYTE      RLDH
      000000D5H   SFRSYM    DATA     BYTE      ADCMPH
      000000CAH   SFRSYM    DATA     BYTE      RLDL
      00000080H.0 SFRSYM    DATA     BIT       P00
      00000090H.0 SFRSYM    DATA     BIT       P10
      00000080H.1 SFRSYM    DATA     BIT       P01
      000000F7H   SFRSYM    DATA     BYTE      I2CMTP
      00000099H   SFRSYM    DATA     BYTE      SBUF
      00000087H   SFRSYM    DATA     BYTE      PCON
      000000A0H.0 SFRSYM    DATA     BIT       P20
      00000090H.1 SFRSYM    DATA     BIT       P11
      00000080H.2 SFRSYM    DATA     BIT       P02
      000000F5H   SFRSYM    DATA     BYTE      I2CMSR
      000000B0H.0 SFRSYM    DATA     BIT       P30
      000000A0H.1 SFRSYM    DATA     BIT       P21
      00000090H.2 SFRSYM    DATA     BIT       P12
      00000080H.3 SFRSYM    DATA     BIT       P03
      000000D4H   SFRSYM    DATA     BYTE      ADCMPL
      000000B0H.1 SFRSYM    DATA     BIT       P31
      000000A0H.2 SFRSYM    DATA     BIT       P22
      00000090H.3 SFRSYM    DATA     BIT       P13
      00000080H.4 SFRSYM    DATA     BIT       P04
      000000B0H.2 SFRSYM    DATA     BIT       P32
      000000A0H.3 SFRSYM    DATA     BIT       P23
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 93


      00000090H.4 SFRSYM    DATA     BIT       P14
      00000080H.5 SFRSYM    DATA     BIT       P05
      00000089H   SFRSYM    DATA     BYTE      TMOD
      00000088H   SFRSYM    DATA     BYTE      TCON
      000000B0H.3 SFRSYM    DATA     BIT       P33
      000000A0H.4 SFRSYM    DATA     BIT       P24
      00000090H.5 SFRSYM    DATA     BIT       P15
      00000080H.6 SFRSYM    DATA     BIT       P06
      000000B0H.4 SFRSYM    DATA     BIT       P34
      000000A0H.5 SFRSYM    DATA     BIT       P25
      00000090H.6 SFRSYM    DATA     BIT       P16
      00000080H.7 SFRSYM    DATA     BIT       P07
      000000B0H.5 SFRSYM    DATA     BIT       P35
      000000A0H.6 SFRSYM    DATA     BIT       P26
      00000090H.7 SFRSYM    DATA     BIT       P17
      000000F2H   SFRSYM    DATA     BYTE      I2CSSR
      000000B0H.6 SFRSYM    DATA     BIT       P36
      000000A0H.7 SFRSYM    DATA     BIT       P27
      000000ECH   SFRSYM    DATA     BYTE      SPCR
      000000DDH   SFRSYM    DATA     BYTE      ADRESH
      000000B0H.7 SFRSYM    DATA     BIT       P37
      000000EEH   SFRSYM    DATA     BYTE      SPDR
      000000EFH   SFRSYM    DATA     BYTE      SSCR
      000000D3H   SFRSYM    DATA     BYTE      ADDLYL
      000000DCH   SFRSYM    DATA     BYTE      ADRESL
      00000088H.1 SFRSYM    DATA     BIT       IE0
      00000088H.3 SFRSYM    DATA     BIT       IE1
      0000008FH   SFRSYM    DATA     BYTE      CLKDIV
      000000F0H   SFRSYM    DATA     BYTE      B
      00000091H   SFRSYM    DATA     BYTE      FUNCCR
      0000009AH   SFRSYM    DATA     BYTE      P0TRIS
      000000A1H   SFRSYM    DATA     BYTE      P1TRIS
      000000A2H   SFRSYM    DATA     BYTE      P2TRIS
      000000E0H   SFRSYM    DATA     BYTE      ACC
      000000A3H   SFRSYM    DATA     BYTE      P3TRIS
      000000A8H.4 SFRSYM    DATA     BIT       ES0
      000000A8H.6 SFRSYM    DATA     BIT       ES1
      000000A8H.1 SFRSYM    DATA     BIT       ET0
      000000EDH   SFRSYM    DATA     BYTE      SPSR
      000000A8H.3 SFRSYM    DATA     BIT       ET1
      00000088H.5 SFRSYM    DATA     BIT       TF0
      000000A8H.5 SFRSYM    DATA     BIT       ET2
      00000098H.0 SFRSYM    DATA     BIT       RI0
      00000088H.7 SFRSYM    DATA     BIT       TF1
      0000008CH   SFRSYM    DATA     BYTE      TH0
      000000A8H.0 SFRSYM    DATA     BIT       EX0
      00000098H.1 SFRSYM    DATA     BIT       TI0
      00000088H.0 SFRSYM    DATA     BIT       IT0
      0000008DH   SFRSYM    DATA     BYTE      TH1
      000000A8H.2 SFRSYM    DATA     BIT       EX1
      00000088H.2 SFRSYM    DATA     BIT       IT1
      000000CDH   SFRSYM    DATA     BYTE      TH2
      000000D0H.0 SFRSYM    DATA     BIT       P
      000000DBH   SFRSYM    DATA     BYTE      TH3
      000000E3H   SFRSYM    DATA     BYTE      TH4
      0000008AH   SFRSYM    DATA     BYTE      TL0
      0000008BH   SFRSYM    DATA     BYTE      TL1
      00000098H.2 SFRSYM    DATA     BIT       U0RB8
      000000CCH   SFRSYM    DATA     BYTE      TL2
      000000B8H.4 SFRSYM    DATA     BIT       PS0
      000000DAH   SFRSYM    DATA     BYTE      TL3
      000000BFH   SFRSYM    DATA     BYTE      BUZCON
      000000B8H.6 SFRSYM    DATA     BIT       PS1
      000000B8H.1 SFRSYM    DATA     BIT       PT0
      00000098H.3 SFRSYM    DATA     BIT       U0TB8
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 94


      000000E2H   SFRSYM    DATA     BYTE      TL4
      000000D0H.3 SFRSYM    DATA     BIT       RS0
      000000B8H.3 SFRSYM    DATA     BIT       PT1
      000000D0H.4 SFRSYM    DATA     BIT       RS1
      000000B8H.5 SFRSYM    DATA     BIT       PT2
      00000098H.7 SFRSYM    DATA     BIT       U0SM0
      00000088H.4 SFRSYM    DATA     BIT       TR0
      000000BEH   SFRSYM    DATA     BYTE      BUZDIV
      00000098H.6 SFRSYM    DATA     BIT       U0SM1
      00000088H.6 SFRSYM    DATA     BIT       TR1
      000000B8H.0 SFRSYM    DATA     BIT       PX0
      00000098H.5 SFRSYM    DATA     BIT       U0SM2
      000000B8H.2 SFRSYM    DATA     BIT       PX1
      000000BDH   SFRSYM    DATA     BYTE      WUTCRH
      00000099H   SFRSYM    DATA     BYTE      SBUF0
      000000EBH   SFRSYM    DATA     BYTE      SBUF1
      000000BCH   SFRSYM    DATA     BYTE      WUTCRL
      00000098H   SFRSYM    DATA     BYTE      SCON0
      000000EAH   SFRSYM    DATA     BYTE      SCON1
      000000C8H   SFRSYM    DATA     BYTE      T2CON
      00000086H   SFRSYM    DATA     BYTE      DPS
      000000FEH   SFRSYM    DATA     BYTE      MDATA
      000000F6H   SFRSYM    DATA     BYTE      I2CMBUF
      000000F1H   SFRSYM    DATA     BYTE      I2CSADR
      00000098H.4 SFRSYM    DATA     BIT       U0REN
      000000C8H.5 SFRSYM    DATA     BIT       CAPES
      000000FDH   SFRSYM    DATA     BYTE      MADRH
      0000008EH   SFRSYM    DATA     BYTE      CKCON
      000000F3H   SFRSYM    DATA     BYTE      I2CSBUF
      000000FCH   SFRSYM    DATA     BYTE      MADRL
      000000D0H.5 SFRSYM    DATA     BIT       F0
      000000FBH   SFRSYM    DATA     BYTE      MLOCK
      000000D0H   SFRSYM    DATA     BYTE      PSW
      00000097H   SFRSYM    DATA     BYTE      WDCON

      010000B6H   BLOCK     CODE     ---       LVL=0
      010000B6H   BLOCK     CODE     NEAR LAB  LVL=1
      00000024H.4 SYMBOL    BIT      BIT       Delay_Open_Buff
      02000000H   SYMBOL    XDATA    INT       Key_Input
      02000002H   SYMBOL    XDATA    INT       Charge_Input
      02000004H   SYMBOL    XDATA    INT       Key_State
      02000006H   SYMBOL    XDATA    INT       Key_State_Save
      02000008H   SYMBOL    XDATA    INT       Charge_State_Save
      0200000AH   SYMBOL    XDATA    INT       Key_Keep_Time_For_System_Open
      00000024H.5 SYMBOL    BIT      BIT       Long_Press_To_Open
      00000024H.6 SYMBOL    BIT      BIT       Blue_Teeth_Long_Press
      0200000CH   SYMBOL    XDATA    INT       Charge_Keep_Time_For_System_Open
      0200000EH   SYMBOL    XDATA    BYTE      UART_Get_CMD
      00000024H.7 SYMBOL    BIT      BIT       Voltage_Low
      0200000FH   SYMBOL    XDATA    WORD      k2k3_press_time
      ---         BLOCKEND  ---      ---       LVL=1
      010000B6H   LINE      CODE     ---       #138
      010000B6H   LINE      CODE     ---       #139
      010000B6H   LINE      CODE     ---       #145
      010000B8H   LINE      CODE     ---       #148
      010000BAH   LINE      CODE     ---       #149
      010000C1H   LINE      CODE     ---       #152
      010000C4H   LINE      CODE     ---       #153
      010000CBH   LINE      CODE     ---       #156
      010000CEH   LINE      CODE     ---       #159
      010000D1H   LINE      CODE     ---       #160
      010000D4H   LINE      CODE     ---       #163
      010000D7H   LINE      CODE     ---       #164
      010000DAH   LINE      CODE     ---       #166
      010000DCH   LINE      CODE     ---       #167
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 95


      010000E3H   LINE      CODE     ---       #168
      010000E7H   LINE      CODE     ---       #169
      010000E9H   LINE      CODE     ---       #170
      010000EFH   LINE      CODE     ---       #171
      010000F5H   LINE      CODE     ---       #172
      010000FBH   LINE      CODE     ---       #173
      010000FDH   LINE      CODE     ---       #174
      01000108H   LINE      CODE     ---       #175
      0100011CH   LINE      CODE     ---       #176
      0100011CH   LINE      CODE     ---       #177
      01000125H   LINE      CODE     ---       #178
      0100012BH   LINE      CODE     ---       #179
      0100012BH   LINE      CODE     ---       #180
      0100012DH   LINE      CODE     ---       #183
      01000130H   LINE      CODE     ---       #185
      01000136H   LINE      CODE     ---       #186
      01000136H   LINE      CODE     ---       #187
      01000139H   LINE      CODE     ---       #188
      0100013BH   LINE      CODE     ---       #190
      0100013EH   LINE      CODE     ---       #191
      01000149H   LINE      CODE     ---       #194
      0100014FH   LINE      CODE     ---       #195
      0100014FH   LINE      CODE     ---       #196
      0100015DH   LINE      CODE     ---       #197
      0100016FH   LINE      CODE     ---       #198
      0100016FH   LINE      CODE     ---       #199
      01000173H   LINE      CODE     ---       #200
      01000175H   LINE      CODE     ---       #201
      0100017BH   LINE      CODE     ---       #202
      0100017DH   LINE      CODE     ---       #203
      0100017FH   LINE      CODE     ---       #204
      01000181H   LINE      CODE     ---       #205
      01000188H   LINE      CODE     ---       #206
      0100018EH   LINE      CODE     ---       #207
      01000190H   LINE      CODE     ---       #208
      01000192H   LINE      CODE     ---       #209
      0100019AH   LINE      CODE     ---       #212
      0100019CH   LINE      CODE     ---       #213
      0100019EH   LINE      CODE     ---       #214
      010001A0H   LINE      CODE     ---       #215
      010001A0H   LINE      CODE     ---       #216
      010001A3H   LINE      CODE     ---       #218
      010001A3H   LINE      CODE     ---       #219
      010001AAH   LINE      CODE     ---       #221
      010001C6H   LINE      CODE     ---       #222
      010001C6H   LINE      CODE     ---       #223
      010001CCH   LINE      CODE     ---       #224
      010001CCH   LINE      CODE     ---       #225
      010001DAH   LINE      CODE     ---       #226
      010001EBH   LINE      CODE     ---       #227
      010001EBH   LINE      CODE     ---       #228
      010001EFH   LINE      CODE     ---       #229
      010001F1H   LINE      CODE     ---       #230
      010001F3H   LINE      CODE     ---       #231
      010001F8H   LINE      CODE     ---       #232
      010001F8H   LINE      CODE     ---       #233
      010001FAH   LINE      CODE     ---       #234
      01000205H   LINE      CODE     ---       #235
      01000205H   LINE      CODE     ---       #236
      01000213H   LINE      CODE     ---       #237
      01000224H   LINE      CODE     ---       #238
      01000224H   LINE      CODE     ---       #239
      01000228H   LINE      CODE     ---       #240
      0100022AH   LINE      CODE     ---       #241
      0100022EH   LINE      CODE     ---       #242
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 96


      01000230H   LINE      CODE     ---       #243
      01000236H   LINE      CODE     ---       #244
      01000236H   LINE      CODE     ---       #245
      01000238H   LINE      CODE     ---       #246
      01000242H   LINE      CODE     ---       #247
      01000242H   LINE      CODE     ---       #248
      01000248H   LINE      CODE     ---       #249
      0100024AH   LINE      CODE     ---       #250
      0100024EH   LINE      CODE     ---       #251
      01000254H   LINE      CODE     ---       #252
      01000256H   LINE      CODE     ---       #253
      01000256H   LINE      CODE     ---       #254
      01000258H   LINE      CODE     ---       #256
      01000262H   LINE      CODE     ---       #257
      01000262H   LINE      CODE     ---       #258
      01000267H   LINE      CODE     ---       #259
      01000267H   LINE      CODE     ---       #260
      01000267H   LINE      CODE     ---       #262
      01000276H   LINE      CODE     ---       #263
      0100028EH   LINE      CODE     ---       #264
      01000291H   LINE      CODE     ---       #266
      01000293H   LINE      CODE     ---       #267
      01000299H   LINE      CODE     ---       #268
      010002A2H   LINE      CODE     ---       #269
      010002A5H   LINE      CODE     ---       #272
      010002AEH   LINE      CODE     ---       #273
      010002AEH   LINE      CODE     ---       #275
      010002B4H   LINE      CODE     ---       #276
      010002B4H   LINE      CODE     ---       #278
      010002B4H   LINE      CODE     ---       #280
      010002B4H   LINE      CODE     ---       #282
      010002B4H   LINE      CODE     ---       #283
      010002B4H   LINE      CODE     ---       #284
      010002B7H   LINE      CODE     ---       #285
      010002B9H   LINE      CODE     ---       #287
      010002BCH   LINE      CODE     ---       #288
      010002C7H   LINE      CODE     ---       #290
      010002D8H   LINE      CODE     ---       #291
      010002F0H   LINE      CODE     ---       #293
      010002F3H   LINE      CODE     ---       #295
      010002F6H   LINE      CODE     ---       #298
      01000303H   LINE      CODE     ---       #299
      01000303H   LINE      CODE     ---       #301
      01000306H   LINE      CODE     ---       #302
      01000306H   LINE      CODE     ---       #304
      01000309H   LINE      CODE     ---       #305
      01000309H   LINE      CODE     ---       #306
      01000311H   LINE      CODE     ---       #307
      01000313H   LINE      CODE     ---       #308
      01000313H   LINE      CODE     ---       #309
      01000313H   LINE      CODE     ---       #310
      01000315H   LINE      CODE     ---       #311
      01000317H   LINE      CODE     ---       #312
      0100031DH   LINE      CODE     ---       #313
      0100031DH   LINE      CODE     ---       #315
      01000320H   LINE      CODE     ---       #316
      01000320H   LINE      CODE     ---       #318
      01000323H   LINE      CODE     ---       #319
      01000323H   LINE      CODE     ---       #320
      0100032BH   LINE      CODE     ---       #321
      0100032DH   LINE      CODE     ---       #322
      0100032DH   LINE      CODE     ---       #323
      0100032DH   LINE      CODE     ---       #324
      0100032FH   LINE      CODE     ---       #325
      01000331H   LINE      CODE     ---       #327
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 97


      01000331H   LINE      CODE     ---       #329
      01000337H   LINE      CODE     ---       #330
      01000337H   LINE      CODE     ---       #332
      0100033FH   LINE      CODE     ---       #333
      01000341H   LINE      CODE     ---       #337
      01000341H   LINE      CODE     ---       #338
      01000343H   LINE      CODE     ---       #339
      01000343H   LINE      CODE     ---       #342
      0100034EH   LINE      CODE     ---       #343
      0100034EH   LINE      CODE     ---       #345
      01000351H   LINE      CODE     ---       #346
      01000351H   LINE      CODE     ---       #348
      01000354H   LINE      CODE     ---       #349
      01000354H   LINE      CODE     ---       #350
      01000356H   LINE      CODE     ---       #351
      01000356H   LINE      CODE     ---       #353
      01000359H   LINE      CODE     ---       #354
      01000359H   LINE      CODE     ---       #355
      0100035BH   LINE      CODE     ---       #356
      0100035BH   LINE      CODE     ---       #359
      01000361H   LINE      CODE     ---       #360
      01000361H   LINE      CODE     ---       #361
      01000363H   LINE      CODE     ---       #362
      01000365H   LINE      CODE     ---       #363
      01000367H   LINE      CODE     ---       #364
      0100036FH   LINE      CODE     ---       #365
      01000371H   LINE      CODE     ---       #367
      01000371H   LINE      CODE     ---       #368
      01000373H   LINE      CODE     ---       #370
      01000373H   LINE      CODE     ---       #374
      0100037FH   LINE      CODE     ---       #375
      0100037FH   LINE      CODE     ---       #376
      0100038DH   LINE      CODE     ---       #377
      0100039CH   LINE      CODE     ---       #378
      0100039CH   LINE      CODE     ---       #379
      0100039EH   LINE      CODE     ---       #380
      010003A3H   LINE      CODE     ---       #381
      010003A5H   LINE      CODE     ---       #382
      010003A7H   LINE      CODE     ---       #383
      010003A9H   LINE      CODE     ---       #384
      010003B1H   LINE      CODE     ---       #385
      010003B3H   LINE      CODE     ---       #386
      010003B9H   LINE      CODE     ---       #387
      010003B9H   LINE      CODE     ---       #388
      010003BBH   LINE      CODE     ---       #390
      010003BBH   LINE      CODE     ---       #391
      010003C2H   LINE      CODE     ---       #392
      010003C4H   LINE      CODE     ---       #393
      010003C4H   LINE      CODE     ---       #394
      010003C4H   LINE      CODE     ---       #397
      010003CAH   LINE      CODE     ---       #398
      010003CAH   LINE      CODE     ---       #399
      010003E7H   LINE      CODE     ---       #400
      010003E7H   LINE      CODE     ---       #401
      010003EAH   LINE      CODE     ---       #402
      010003EAH   LINE      CODE     ---       #404
      010003EAH   LINE      CODE     ---       #405
      010003EAH   LINE      CODE     ---       #406
      010003EAH   LINE      CODE     ---       #407
      010003EAH   LINE      CODE     ---       #408
      010003EAH   LINE      CODE     ---       #409
      010003EAH   LINE      CODE     ---       #410
      010003ECH   LINE      CODE     ---       #411
      010003F7H   LINE      CODE     ---       #412
      010003F7H   LINE      CODE     ---       #414
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 98


      010003F7H   LINE      CODE     ---       #415
      010003F7H   LINE      CODE     ---       #416
      010003F7H   LINE      CODE     ---       #417
      010003F7H   LINE      CODE     ---       #419
      010003F9H   LINE      CODE     ---       #420
      010003FFH   LINE      CODE     ---       #421
      010003FFH   LINE      CODE     ---       #423
      01000404H   LINE      CODE     ---       #424
      01000404H   LINE      CODE     ---       #425
      01000404H   LINE      CODE     ---       #426
      01000404H   LINE      CODE     ---       #427
      01000404H   LINE      CODE     ---       #428
      01000404H   LINE      CODE     ---       #429
      01000404H   LINE      CODE     ---       #430
      01000406H   LINE      CODE     ---       #431
      01000423H   LINE      CODE     ---       #432
      01000423H   LINE      CODE     ---       #433
      01000426H   LINE      CODE     ---       #434
      01000426H   LINE      CODE     ---       #436
      0100042BH   LINE      CODE     ---       #437
      0100042BH   LINE      CODE     ---       #438
      0100042BH   LINE      CODE     ---       #439
      0100042BH   LINE      CODE     ---       #440
      0100042BH   LINE      CODE     ---       #441
      0100042BH   LINE      CODE     ---       #442
      0100042DH   LINE      CODE     ---       #443
      01000438H   LINE      CODE     ---       #444
      01000438H   LINE      CODE     ---       #446
      0100043AH   LINE      CODE     ---       #447
      0100043CH   LINE      CODE     ---       #448
      01000444H   LINE      CODE     ---       #449
      01000446H   LINE      CODE     ---       #451
      01000448H   LINE      CODE     ---       #452
      0100044EH   LINE      CODE     ---       #453
      0100044EH   LINE      CODE     ---       #455
      01000454H   LINE      CODE     ---       #456
      0100045DH   LINE      CODE     ---       #457
      0100045FH   LINE      CODE     ---       #458
      01000461H   LINE      CODE     ---       #459
      01000463H   LINE      CODE     ---       #460
      0100046AH   LINE      CODE     ---       #461
      0100046AH   LINE      CODE     ---       #462
      0100046AH   LINE      CODE     ---       #463
      0100046AH   LINE      CODE     ---       #466
      01000470H   LINE      CODE     ---       #467
      01000470H   LINE      CODE     ---       #468
      01000472H   LINE      CODE     ---       #469
      01000480H   LINE      CODE     ---       #471
      01000491H   LINE      CODE     ---       #472
      01000491H   LINE      CODE     ---       #473
      01000499H   LINE      CODE     ---       #474
      0100049BH   LINE      CODE     ---       #476
      0100049BH   LINE      CODE     ---       #477
      010004A2H   LINE      CODE     ---       #478
      010004A2H   LINE      CODE     ---       #479
      010004A2H   LINE      CODE     ---       #482
      010004A5H   LINE      CODE     ---       #483
      010004A5H   LINE      CODE     ---       #485
      010004B3H   LINE      CODE     ---       #486
      010004C5H   LINE      CODE     ---       #487
      010004C5H   LINE      CODE     ---       #488
      010004C9H   LINE      CODE     ---       #489
      010004CBH   LINE      CODE     ---       #490
      010004CBH   LINE      CODE     ---       #491
      010004CEH   LINE      CODE     ---       #493
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 99


      010004CEH   LINE      CODE     ---       #495
      010004D0H   LINE      CODE     ---       #496
      010004D7H   LINE      CODE     ---       #497
      010004D7H   LINE      CODE     ---       #498
      010004DAH   LINE      CODE     ---       #501
      010004DAH   LINE      CODE     ---       #503
      010004DDH   LINE      CODE     ---       #506
      010004FDH   LINE      CODE     ---       #507
      010004FDH   LINE      CODE     ---       #508
      010004FFH   LINE      CODE     ---       #509
      01000501H   LINE      CODE     ---       #511
      0100051EH   LINE      CODE     ---       #512
      0100051EH   LINE      CODE     ---       #513
      01000526H   LINE      CODE     ---       #514
      01000528H   LINE      CODE     ---       #515
      01000538H   LINE      CODE     ---       #516
      01000538H   LINE      CODE     ---       #517
      01000546H   LINE      CODE     ---       #518
      01000557H   LINE      CODE     ---       #519
      01000557H   LINE      CODE     ---       #520
      0100055BH   LINE      CODE     ---       #521
      0100055EH   LINE      CODE     ---       #522
      01000560H   LINE      CODE     ---       #523
      01000562H   LINE      CODE     ---       #524
      01000565H   LINE      CODE     ---       #525
      01000565H   LINE      CODE     ---       #526
      01000565H   LINE      CODE     ---       #527
      01000565H   LINE      CODE     ---       #529
      01000574H   LINE      CODE     ---       #532
      0100057AH   LINE      CODE     ---       #533
      0100057AH   LINE      CODE     ---       #534
      01000587H   LINE      CODE     ---       #535
      01000587H   LINE      CODE     ---       #536
      01000589H   LINE      CODE     ---       #537
      0100058BH   LINE      CODE     ---       #538
      01000593H   LINE      CODE     ---       #539
      01000595H   LINE      CODE     ---       #541
      01000595H   LINE      CODE     ---       #542
      0100059BH   LINE      CODE     ---       #543
      0100059BH   LINE      CODE     ---       #545
      010005A3H   LINE      CODE     ---       #546
      010005A3H   LINE      CODE     ---       #547
      010005A4H   LINE      CODE     ---       #548
      010005A8H   LINE      CODE     ---       #549
      010005ABH   LINE      CODE     ---       #550
      010005B4H   LINE      CODE     ---       #551
      010005B4H   LINE      CODE     ---       #552
      010005B9H   LINE      CODE     ---       #554
      01000602H   LINE      CODE     ---       #555
      01000602H   LINE      CODE     ---       #556
      01000602H   LINE      CODE     ---       #557
      01000602H   LINE      CODE     ---       #558
      01000610H   LINE      CODE     ---       #559
      0100061DH   LINE      CODE     ---       #560
      0100061DH   LINE      CODE     ---       #561
      01000624H   LINE      CODE     ---       #562
      01000626H   LINE      CODE     ---       #564
      01000626H   LINE      CODE     ---       #565
      01000628H   LINE      CODE     ---       #566
      0100062FH   LINE      CODE     ---       #567
      0100062FH   LINE      CODE     ---       #568
      0100062FH   LINE      CODE     ---       #569
      0100062FH   LINE      CODE     ---       #570
      0100062FH   LINE      CODE     ---       #571
      01000631H   LINE      CODE     ---       #572
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 100


      01000631H   LINE      CODE     ---       #573
      0100063AH   LINE      CODE     ---       #574
      01000645H   LINE      CODE     ---       #575
      01000645H   LINE      CODE     ---       #576
      01000647H   LINE      CODE     ---       #577
      01000647H   LINE      CODE     ---       #578
      01000647H   LINE      CODE     ---       #579
      01000647H   LINE      CODE     ---       #580
      01000647H   LINE      CODE     ---       #581
      01000647H   LINE      CODE     ---       #582
      0100064AH   LINE      CODE     ---       #583
      0100064AH   LINE      CODE     ---       #584
      0100064AH   LINE      CODE     ---       #585
      01000658H   LINE      CODE     ---       #586
      01000665H   LINE      CODE     ---       #587
      01000665H   LINE      CODE     ---       #588
      0100066BH   LINE      CODE     ---       #589
      0100066DH   LINE      CODE     ---       #591
      0100066DH   LINE      CODE     ---       #592
      0100066FH   LINE      CODE     ---       #593
      01000676H   LINE      CODE     ---       #594
      01000676H   LINE      CODE     ---       #595
      01000681H   LINE      CODE     ---       #596
      01000686H   LINE      CODE     ---       #597
      01000686H   LINE      CODE     ---       #598
      01000689H   LINE      CODE     ---       #599
      01000689H   LINE      CODE     ---       #600
      01000692H   LINE      CODE     ---       #601
      0100069AH   LINE      CODE     ---       #602
      0100069AH   LINE      CODE     ---       #603
      0100069CH   LINE      CODE     ---       #604
      0100069CH   LINE      CODE     ---       #605
      0100069CH   LINE      CODE     ---       #606
      0100069CH   LINE      CODE     ---       #607
      0100069CH   LINE      CODE     ---       #608
      0100069CH   LINE      CODE     ---       #609
      0100069EH   LINE      CODE     ---       #610
      0100069EH   LINE      CODE     ---       #611
      010006A7H   LINE      CODE     ---       #612
      010006AFH   LINE      CODE     ---       #613
      010006AFH   LINE      CODE     ---       #614
      010006B1H   LINE      CODE     ---       #615
      010006B1H   LINE      CODE     ---       #616
      010006B6H   LINE      CODE     ---       #617
      010006B6H   LINE      CODE     ---       #618
      010006B6H   LINE      CODE     ---       #619
      010006B6H   LINE      CODE     ---       #620
      010006B8H   LINE      CODE     ---       #621
      010006B8H   LINE      CODE     ---       #622
      010006C1H   LINE      CODE     ---       #623
      010006C9H   LINE      CODE     ---       #624
      010006C9H   LINE      CODE     ---       #625
      010006CBH   LINE      CODE     ---       #626
      010006CBH   LINE      CODE     ---       #627
      010006D1H   LINE      CODE     ---       #628
      010006D3H   LINE      CODE     ---       #629
      010006D3H   LINE      CODE     ---       #630
      010006D3H   LINE      CODE     ---       #631
      010006D6H   LINE      CODE     ---       #632
      010006D6H   LINE      CODE     ---       #633
      010006DCH   LINE      CODE     ---       #634
      010006DEH   LINE      CODE     ---       #635
      010006E6H   LINE      CODE     ---       #636
      010006E8H   LINE      CODE     ---       #637
      010006EBH   LINE      CODE     ---       #638
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 101


      010006EBH   LINE      CODE     ---       #639
      010006EBH   LINE      CODE     ---       #640
      010006F4H   LINE      CODE     ---       #641
      010006FCH   LINE      CODE     ---       #642
      010006FCH   LINE      CODE     ---       #643
      010006FEH   LINE      CODE     ---       #644
      010006FEH   LINE      CODE     ---       #645
      01000704H   LINE      CODE     ---       #646
      01000706H   LINE      CODE     ---       #647
      01000706H   LINE      CODE     ---       #648
      01000706H   LINE      CODE     ---       #649
      01000706H   LINE      CODE     ---       #650
      01000708H   LINE      CODE     ---       #651
      01000708H   LINE      CODE     ---       #652
      01000708H   LINE      CODE     ---       #653
      01000711H   LINE      CODE     ---       #654
      01000719H   LINE      CODE     ---       #655
      01000719H   LINE      CODE     ---       #656
      0100071BH   LINE      CODE     ---       #657
      0100071BH   LINE      CODE     ---       #658
      01000721H   LINE      CODE     ---       #659
      01000723H   LINE      CODE     ---       #660
      01000723H   LINE      CODE     ---       #661
      01000723H   LINE      CODE     ---       #662
      01000723H   LINE      CODE     ---       #663
      01000725H   LINE      CODE     ---       #664
      01000725H   LINE      CODE     ---       #665
      01000725H   LINE      CODE     ---       #666
      0100072EH   LINE      CODE     ---       #667
      01000736H   LINE      CODE     ---       #668
      01000736H   LINE      CODE     ---       #669
      01000738H   LINE      CODE     ---       #670
      01000738H   LINE      CODE     ---       #671
      0100073EH   LINE      CODE     ---       #672
      01000740H   LINE      CODE     ---       #673
      01000740H   LINE      CODE     ---       #674
      01000740H   LINE      CODE     ---       #675
      01000740H   LINE      CODE     ---       #676
      01000742H   LINE      CODE     ---       #677
      01000742H   LINE      CODE     ---       #678
      01000742H   LINE      CODE     ---       #679
      0100074BH   LINE      CODE     ---       #680
      01000753H   LINE      CODE     ---       #681
      01000753H   LINE      CODE     ---       #682
      01000755H   LINE      CODE     ---       #683
      01000755H   LINE      CODE     ---       #684
      0100075BH   LINE      CODE     ---       #685
      0100075DH   LINE      CODE     ---       #686
      0100075FH   LINE      CODE     ---       #687
      01000765H   LINE      CODE     ---       #688
      01000765H   LINE      CODE     ---       #689
      01000767H   LINE      CODE     ---       #690
      01000767H   LINE      CODE     ---       #691
      01000767H   LINE      CODE     ---       #692
      0100076DH   LINE      CODE     ---       #693
      0100076FH   LINE      CODE     ---       #694
      0100076FH   LINE      CODE     ---       #695
      0100076FH   LINE      CODE     ---       #696
      0100076FH   LINE      CODE     ---       #697
      0100076FH   LINE      CODE     ---       #698
      0100076FH   LINE      CODE     ---       #699
      0100076FH   LINE      CODE     ---       #700
      0100076FH   LINE      CODE     ---       #703
      01000780H   LINE      CODE     ---       #704
      01000780H   LINE      CODE     ---       #705
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 102


      01000782H   LINE      CODE     ---       #706
      01000784H   LINE      CODE     ---       #707
      01000792H   LINE      CODE     ---       #709
      0100079EH   LINE      CODE     ---       #710
      0100079EH   LINE      CODE     ---       #711
      010007A0H   LINE      CODE     ---       #712
      010007A2H   LINE      CODE     ---       #713
      010007A4H   LINE      CODE     ---       #714
      010007A4H   LINE      CODE     ---       #715
      010007AAH   LINE      CODE     ---       #716
      010007AAH   LINE      CODE     ---       #717
      010007ADH   LINE      CODE     ---       #718
      010007B4H   LINE      CODE     ---       #719
      010007B6H   LINE      CODE     ---       #720
      010007B6H   LINE      CODE     ---       #721
      010007B9H   LINE      CODE     ---       #722
      010007B9H   LINE      CODE     ---       #723
      010007C2H   LINE      CODE     ---       #724
      010007C2H   LINE      CODE     ---       #725
      010007C5H   LINE      CODE     ---       #726
      010007C5H   LINE      CODE     ---       #728
      010007C8H   LINE      CODE     ---       #729
      010007C8H   LINE      CODE     ---       #730
      010007D1H   LINE      CODE     ---       #731
      010007D3H   LINE      CODE     ---       #733
      010007D3H   LINE      CODE     ---       #735
      010007E0H   LINE      CODE     ---       #736
      010007E0H   LINE      CODE     ---       #737
      010007E8H   LINE      CODE     ---       #738
      010007EAH   LINE      CODE     ---       #739
      010007F0H   LINE      CODE     ---       #740
      010007F0H   LINE      CODE     ---       #741
      010007F8H   LINE      CODE     ---       #742
      010007F8H   LINE      CODE     ---       #743
      010007F8H   LINE      CODE     ---       #744
      010007F8H   LINE      CODE     ---       #746
      010007FBH   LINE      CODE     ---       #747
      010007FBH   LINE      CODE     ---       #749
      01000801H   LINE      CODE     ---       #750
      01000801H   LINE      CODE     ---       #751
      01000812H   LINE      CODE     ---       #752
      01000812H   LINE      CODE     ---       #753
      01000812H   LINE      CODE     ---       #754
      01000814H   LINE      CODE     ---       #756
      01000814H   LINE      CODE     ---       #757
      0100081CH   LINE      CODE     ---       #758
      01000823H   LINE      CODE     ---       #759
      01000829H   LINE      CODE     ---       #760
      0100082BH   LINE      CODE     ---       #761
      0100082DH   LINE      CODE     ---       #762
      0100082FH   LINE      CODE     ---       #763
      01000831H   LINE      CODE     ---       #764
      01000833H   LINE      CODE     ---       #765
      01000833H   LINE      CODE     ---       #766
      01000836H   LINE      CODE     ---       #768
      01000836H   LINE      CODE     ---       #770
      0100083EH   LINE      CODE     ---       #771
      01000845H   LINE      CODE     ---       #772
      0100084BH   LINE      CODE     ---       #773
      0100084DH   LINE      CODE     ---       #774
      0100084FH   LINE      CODE     ---       #775
      01000851H   LINE      CODE     ---       #776
      01000853H   LINE      CODE     ---       #777
      01000853H   LINE      CODE     ---       #778
      01000856H   LINE      CODE     ---       #779
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 103


      01000867H   LINE      CODE     ---       #780
      01000867H   LINE      CODE     ---       #781
      0100086FH   LINE      CODE     ---       #782
      01000872H   LINE      CODE     ---       #784
      01000872H   LINE      CODE     ---       #786
      01000878H   LINE      CODE     ---       #787
      01000878H   LINE      CODE     ---       #788
      01000878H   LINE      CODE     ---       #789
      01000878H   LINE      CODE     ---       #790
      01000878H   LINE      CODE     ---       #791
      01000878H   LINE      CODE     ---       #792
      01000878H   LINE      CODE     ---       #793
      01000878H   LINE      CODE     ---       #794
      0100087AH   LINE      CODE     ---       #796
      0100087AH   LINE      CODE     ---       #799
      01000880H   LINE      CODE     ---       #800
      01000880H   LINE      CODE     ---       #801
      01000887H   LINE      CODE     ---       #802
      0100088AH   LINE      CODE     ---       #804
      0100088AH   LINE      CODE     ---       #806
      01000892H   LINE      CODE     ---       #807
      01000899H   LINE      CODE     ---       #808
      0100089FH   LINE      CODE     ---       #809
      010008A1H   LINE      CODE     ---       #810
      010008A3H   LINE      CODE     ---       #811
      010008A5H   LINE      CODE     ---       #812
      010008A5H   LINE      CODE     ---       #813
      010008A5H   LINE      CODE     ---       #814
      010008A5H   LINE      CODE     ---       #815
      010008A8H   LINE      CODE     ---       #816
      010008B3H   LINE      CODE     ---       #817
      010008B3H   LINE      CODE     ---       #818
      010008B5H   LINE      CODE     ---       #819
      010008B7H   LINE      CODE     ---       #820
      010008B9H   LINE      CODE     ---       #821
      010008BBH   LINE      CODE     ---       #822
      010008BDH   LINE      CODE     ---       #823
      010008C0H   LINE      CODE     ---       #824
      010008C9H   LINE      CODE     ---       #825
      010008C9H   LINE      CODE     ---       #827
      010008C9H   LINE      CODE     ---       #828
      010008D0H   LINE      CODE     ---       #829
      010008D0H   LINE      CODE     ---       #830
      010008D2H   LINE      CODE     ---       #831
      010008D4H   LINE      CODE     ---       #832
      010008D6H   LINE      CODE     ---       #833
      010008D8H   LINE      CODE     ---       #834
      010008DAH   LINE      CODE     ---       #835
      010008DCH   LINE      CODE     ---       #836
      010008DEH   LINE      CODE     ---       #839
      010008E0H   LINE      CODE     ---       #840
      010008E2H   LINE      CODE     ---       #841
      010008E4H   LINE      CODE     ---       #842
      010008EAH   LINE      CODE     ---       #843
      010008F0H   LINE      CODE     ---       #844
      010008F2H   LINE      CODE     ---       #845
      010008F4H   LINE      CODE     ---       #846
      010008F6H   LINE      CODE     ---       #847
      010008F8H   LINE      CODE     ---       #850
      010008FBH   LINE      CODE     ---       #853
      010008FEH   LINE      CODE     ---       #856
      01000901H   LINE      CODE     ---       #859
      01000904H   LINE      CODE     ---       #860
      01000907H   LINE      CODE     ---       #861
      0100090AH   LINE      CODE     ---       #862
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 104


      0100090DH   LINE      CODE     ---       #863
      01000910H   LINE      CODE     ---       #864
      01000913H   LINE      CODE     ---       #865
      01000916H   LINE      CODE     ---       #868
      0100091CH   LINE      CODE     ---       #869
      0100091CH   LINE      CODE     ---       #870
      ---         BLOCKEND  ---      ---       LVL=0

      010025A5H   BLOCK     CODE     ---       LVL=0
      00000006H   SYMBOL    DATA     WORD      z
      010025A5H   BLOCK     CODE     NEAR LAB  LVL=1
      00000002H   SYMBOL    DATA     WORD      x
      00000004H   SYMBOL    DATA     WORD      y
      ---         BLOCKEND  ---      ---       LVL=1
      010025A5H   LINE      CODE     ---       #875
      010025A5H   LINE      CODE     ---       #876
      010025A5H   LINE      CODE     ---       #878
      010025AFH   LINE      CODE     ---       #879
      010025C4H   LINE      CODE     ---       #880
      ---         BLOCKEND  ---      ---       LVL=0

      01001E25H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Direction
      01001E25H   BLOCK     CODE     NEAR LAB  LVL=1
      02000050H   SYMBOL    XDATA    BYTE      Step_No
      ---         BLOCKEND  ---      ---       LVL=1
      01001E25H   LINE      CODE     ---       #882
      01001E25H   LINE      CODE     ---       #883
      01001E25H   LINE      CODE     ---       #886
      01001E2AH   LINE      CODE     ---       #887
      01001E2AH   LINE      CODE     ---       #888
      01001E30H   LINE      CODE     ---       #889
      01001E3AH   LINE      CODE     ---       #890
      01001E3AH   LINE      CODE     ---       #891
      01001E3CH   LINE      CODE     ---       #892
      01001E3CH   LINE      CODE     ---       #893
      01001E3EH   LINE      CODE     ---       #895
      01001E3EH   LINE      CODE     ---       #896
      01001E4BH   LINE      CODE     ---       #897
      01001E4BH   LINE      CODE     ---       #898
      01001E4EH   LINE      CODE     ---       #899
      01001E4EH   LINE      CODE     ---       #900
      01001E54H   LINE      CODE     ---       #901
      01001E54H   LINE      CODE     ---       #903
      01001E7CH   LINE      CODE     ---       #904
      01001E7CH   LINE      CODE     ---       #905
      01001E80H   LINE      CODE     ---       #906
      01001E84H   LINE      CODE     ---       #907
      01001E8AH   LINE      CODE     ---       #908
      01001E90H   LINE      CODE     ---       #909
      01001E99H   LINE      CODE     ---       #910
      01001EA2H   LINE      CODE     ---       #911
      01001EA6H   LINE      CODE     ---       #912
      01001EAFH   LINE      CODE     ---       #913
      01001EAFH   LINE      CODE     ---       #914
      01001EB7H   LINE      CODE     ---       #916
      01001EB7H   LINE      CODE     ---       #917
      01001EB7H   LINE      CODE     ---       #918
      ---         BLOCKEND  ---      ---       LVL=0

      010022F4H   BLOCK     CODE     ---       LVL=0
      00000007H   SYMBOL    DATA     BYTE      Key_Input
      010022F4H   LINE      CODE     ---       #920
      010022F4H   LINE      CODE     ---       #921
      010022F4H   LINE      CODE     ---       #922
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 105


      01002304H   LINE      CODE     ---       #923
      01002304H   LINE      CODE     ---       #924
      01002304H   LINE      CODE     ---       #925
      0100230DH   LINE      CODE     ---       #926
      01002313H   LINE      CODE     ---       #927
      01002314H   LINE      CODE     ---       #928
      01002314H   LINE      CODE     ---       #929
      01002317H   LINE      CODE     ---       #930
      01002317H   LINE      CODE     ---       #931
      0100231CH   LINE      CODE     ---       #932
      0100231DH   LINE      CODE     ---       #934
      0100231DH   LINE      CODE     ---       #935
      01002323H   LINE      CODE     ---       #936
      01002323H   LINE      CODE     ---       #937
      01002324H   LINE      CODE     ---       #938
      01002324H   LINE      CODE     ---       #939
      0100232DH   LINE      CODE     ---       #940
      01002333H   LINE      CODE     ---       #941
      01002334H   LINE      CODE     ---       #942
      01002334H   LINE      CODE     ---       #943
      0100233AH   LINE      CODE     ---       #944
      0100233AH   LINE      CODE     ---       #945
      0100233AH   LINE      CODE     ---       #946
      0100233AH   LINE      CODE     ---       #947
      0100233AH   LINE      CODE     ---       #948
      ---         BLOCKEND  ---      ---       LVL=0

      0100208BH   BLOCK     CODE     ---       LVL=0
      0100208BH   BLOCK     CODE     NEAR LAB  LVL=1
      02000051H   SYMBOL    XDATA    BYTE      motor_delay_cnt
      ---         BLOCKEND  ---      ---       LVL=1
      0100208BH   LINE      CODE     ---       #951
      0100208BH   LINE      CODE     ---       #952
      0100208BH   LINE      CODE     ---       #956
      0100208EH   LINE      CODE     ---       #957
      0100208EH   LINE      CODE     ---       #958
      01002094H   LINE      CODE     ---       #959
      0100209EH   LINE      CODE     ---       #960
      0100209EH   LINE      CODE     ---       #962
      0100209EH   LINE      CODE     ---       #963
      010020A0H   LINE      CODE     ---       #964
      010020A0H   LINE      CODE     ---       #966
      010020ADH   LINE      CODE     ---       #968
      010020B5H   LINE      CODE     ---       #969
      010020B5H   LINE      CODE     ---       #970
      010020BBH   LINE      CODE     ---       #971
      010020C6H   LINE      CODE     ---       #972
      010020C8H   LINE      CODE     ---       #974
      010020C8H   LINE      CODE     ---       #975
      010020CAH   LINE      CODE     ---       #976
      010020CFH   LINE      CODE     ---       #977
      010020CFH   LINE      CODE     ---       #979
      010020DEH   LINE      CODE     ---       #980
      010020DEH   LINE      CODE     ---       #981
      010020E4H   LINE      CODE     ---       #982
      010020EFH   LINE      CODE     ---       #983
      010020F0H   LINE      CODE     ---       #985
      010020F0H   LINE      CODE     ---       #986
      010020F5H   LINE      CODE     ---       #987
      010020F7H   LINE      CODE     ---       #988
      010020F7H   LINE      CODE     ---       #989
      ---         BLOCKEND  ---      ---       LVL=0

      0100244EH   BLOCK     CODE     ---       LVL=0
      02000011H   SYMBOL    XDATA    WORD      dly1
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 106


      0100244EH   LINE      CODE     ---       #991
      01002456H   LINE      CODE     ---       #992
      01002456H   LINE      CODE     ---       #993
      01002459H   LINE      CODE     ---       #994
      0100245CH   LINE      CODE     ---       #995
      01002465H   LINE      CODE     ---       #996
      01002474H   LINE      CODE     ---       #997
      01002477H   LINE      CODE     ---       #998
      0100247AH   LINE      CODE     ---       #999
      ---         BLOCKEND  ---      ---       LVL=0

      010024F5H   BLOCK     CODE     ---       LVL=0
      010024F5H   BLOCK     CODE     NEAR LAB  LVL=1
      02000011H   SYMBOL    XDATA    WORD      temp
      ---         BLOCKEND  ---      ---       LVL=1
      010024F5H   LINE      CODE     ---       #1001
      010024F5H   LINE      CODE     ---       #1002
      010024F5H   LINE      CODE     ---       #1005
      010024F8H   LINE      CODE     ---       #1006
      010024FBH   LINE      CODE     ---       #1007
      0100250CH   LINE      CODE     ---       #1008
      0100250FH   LINE      CODE     ---       #1009
      01002512H   LINE      CODE     ---       #1011
      0100251AH   LINE      CODE     ---       #1012
      ---         BLOCKEND  ---      ---       LVL=0

      01001A9DH   BLOCK     CODE     ---       LVL=0
      01001A9DH   LINE      CODE     ---       #1014
      01001A9DH   LINE      CODE     ---       #1015
      01001A9DH   LINE      CODE     ---       #1017
      01001AA8H   LINE      CODE     ---       #1018
      01001AA8H   LINE      CODE     ---       #1020
      01001AABH   LINE      CODE     ---       #1021
      01001AABH   LINE      CODE     ---       #1023
      01001AADH   LINE      CODE     ---       #1024
      01001AB0H   LINE      CODE     ---       #1025
      01001AB0H   LINE      CODE     ---       #1027
      01001AB7H   LINE      CODE     ---       #1028
      01001AB9H   LINE      CODE     ---       #1029
      01001ABBH   LINE      CODE     ---       #1031
      01001ABBH   LINE      CODE     ---       #1032
      01001ABDH   LINE      CODE     ---       #1033
      01001ABDH   LINE      CODE     ---       #1034
      01001ABFH   LINE      CODE     ---       #1036
      01001ABFH   LINE      CODE     ---       #1038
      01001AC1H   LINE      CODE     ---       #1039
      01001AC4H   LINE      CODE     ---       #1040
      01001AC4H   LINE      CODE     ---       #1042
      01001AC7H   LINE      CODE     ---       #1043
      01001AC7H   LINE      CODE     ---       #1044
      01001AC9H   LINE      CODE     ---       #1045
      01001ACBH   LINE      CODE     ---       #1047
      01001ACBH   LINE      CODE     ---       #1048
      01001ACDH   LINE      CODE     ---       #1049
      01001ACDH   LINE      CODE     ---       #1050
      01001ACFH   LINE      CODE     ---       #1052
      01001ACFH   LINE      CODE     ---       #1054
      01001AD1H   LINE      CODE     ---       #1055
      01001AD1H   LINE      CODE     ---       #1056
      01001AD1H   LINE      CODE     ---       #1057
      01001AD3H   LINE      CODE     ---       #1059
      01001AD3H   LINE      CODE     ---       #1061
      01001AD6H   LINE      CODE     ---       #1062
      01001AD6H   LINE      CODE     ---       #1063
      01001AD8H   LINE      CODE     ---       #1064
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 107


      01001ADBH   LINE      CODE     ---       #1065
      01001ADBH   LINE      CODE     ---       #1067
      01001AE2H   LINE      CODE     ---       #1068
      01001AE4H   LINE      CODE     ---       #1069
      01001AE6H   LINE      CODE     ---       #1071
      01001AE6H   LINE      CODE     ---       #1072
      01001AE8H   LINE      CODE     ---       #1073
      01001AE8H   LINE      CODE     ---       #1074
      01001AEAH   LINE      CODE     ---       #1076
      01001AEAH   LINE      CODE     ---       #1077
      01001AEDH   LINE      CODE     ---       #1078
      01001AEDH   LINE      CODE     ---       #1079
      01001AEFH   LINE      CODE     ---       #1080
      01001AF9H   LINE      CODE     ---       #1082
      01001AF9H   LINE      CODE     ---       #1083
      01001B00H   LINE      CODE     ---       #1084
      01001B02H   LINE      CODE     ---       #1085
      01001B02H   LINE      CODE     ---       #1086
      01001B04H   LINE      CODE     ---       #1088
      01001B04H   LINE      CODE     ---       #1089
      01001B06H   LINE      CODE     ---       #1090
      01001B10H   LINE      CODE     ---       #1092
      01001B10H   LINE      CODE     ---       #1093
      01001B17H   LINE      CODE     ---       #1094
      01001B19H   LINE      CODE     ---       #1095
      01001B19H   LINE      CODE     ---       #1096
      01001B19H   LINE      CODE     ---       #1097
      01001B19H   LINE      CODE     ---       #1098
      01001B19H   LINE      CODE     ---       #1101
      01001B1CH   LINE      CODE     ---       #1102
      01001B1CH   LINE      CODE     ---       #1103
      01001B2AH   LINE      CODE     ---       #1104
      01001B39H   LINE      CODE     ---       #1105
      01001B39H   LINE      CODE     ---       #1106
      01001B3BH   LINE      CODE     ---       #1107
      01001B3DH   LINE      CODE     ---       #1109
      01001B3DH   LINE      CODE     ---       #1110
      01001B3FH   LINE      CODE     ---       #1111
      01001B41H   LINE      CODE     ---       #1112
      01001B48H   LINE      CODE     ---       #1113
      01001B48H   LINE      CODE     ---       #1114
      01001B48H   LINE      CODE     ---       #1117
      01001B4EH   LINE      CODE     ---       #1118
      01001B58H   LINE      CODE     ---       #1119
      01001B58H   LINE      CODE     ---       #1120
      01001B5AH   LINE      CODE     ---       #1121
      01001B5FH   LINE      CODE     ---       #1122
      01001B5FH   LINE      CODE     ---       #1125
      01001B6DH   LINE      CODE     ---       #1126
      01001B7CH   LINE      CODE     ---       #1127
      01001B7CH   LINE      CODE     ---       #1128
      01001B80H   LINE      CODE     ---       #1129
      01001B85H   LINE      CODE     ---       #1130
      01001B85H   LINE      CODE     ---       #1133
      01001B8DH   LINE      CODE     ---       #1134
      01001B8DH   LINE      CODE     ---       #1135
      01001B93H   LINE      CODE     ---       #1136
      01001B9DH   LINE      CODE     ---       #1137
      01001B9DH   LINE      CODE     ---       #1138
      01001B9FH   LINE      CODE     ---       #1139
      01001BA4H   LINE      CODE     ---       #1140
      01001BA4H   LINE      CODE     ---       #1141
      01001BA5H   LINE      CODE     ---       #1143
      01001BA5H   LINE      CODE     ---       #1144
      01001BAAH   LINE      CODE     ---       #1145
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 108


      01001BACH   LINE      CODE     ---       #1146
      01001BACH   LINE      CODE     ---       #1147
      ---         BLOCKEND  ---      ---       LVL=0

      010015B4H   BLOCK     CODE     ---       LVL=0
      010015B4H   LINE      CODE     ---       #1156
      010015B4H   LINE      CODE     ---       #1157
      010015B4H   LINE      CODE     ---       #1159
      010015C0H   LINE      CODE     ---       #1160
      010015C0H   LINE      CODE     ---       #1161
      010015C3H   LINE      CODE     ---       #1162
      010015C3H   LINE      CODE     ---       #1163
      010015C5H   LINE      CODE     ---       #1164
      010015C7H   LINE      CODE     ---       #1165
      010015C9H   LINE      CODE     ---       #1166
      010015CBH   LINE      CODE     ---       #1167
      010015CDH   LINE      CODE     ---       #1168
      010015D3H   LINE      CODE     ---       #1169
      010015D3H   LINE      CODE     ---       #1170
      010015D3H   LINE      CODE     ---       #1173
      010015F4H   LINE      CODE     ---       #1174
      010015F4H   LINE      CODE     ---       #1176
      010015FAH   LINE      CODE     ---       #1177
      010015FAH   LINE      CODE     ---       #1178
      01001603H   LINE      CODE     ---       #1179
      01001609H   LINE      CODE     ---       #1180
      0100160BH   LINE      CODE     ---       #1181
      0100160DH   LINE      CODE     ---       #1182
      0100160FH   LINE      CODE     ---       #1183
      01001611H   LINE      CODE     ---       #1184
      01001615H   LINE      CODE     ---       #1185
      01001615H   LINE      CODE     ---       #1186
      01001615H   LINE      CODE     ---       #1189
      01001618H   LINE      CODE     ---       #1190
      01001618H   LINE      CODE     ---       #1191
      0100161AH   LINE      CODE     ---       #1193
      01001635H   LINE      CODE     ---       #1194
      01001635H   LINE      CODE     ---       #1196
      01001637H   LINE      CODE     ---       #1197
      01001639H   LINE      CODE     ---       #1198
      0100163BH   LINE      CODE     ---       #1201
      01001644H   LINE      CODE     ---       #1202
      0100164AH   LINE      CODE     ---       #1203
      0100164CH   LINE      CODE     ---       #1204
      0100164EH   LINE      CODE     ---       #1205
      01001652H   LINE      CODE     ---       #1206
      01001659H   LINE      CODE     ---       #1207
      0100165BH   LINE      CODE     ---       #1208
      0100166AH   LINE      CODE     ---       #1209
      0100166AH   LINE      CODE     ---       #1211
      01001670H   LINE      CODE     ---       #1212
      01001672H   LINE      CODE     ---       #1213
      01001674H   LINE      CODE     ---       #1214
      01001676H   LINE      CODE     ---       #1215
      01001678H   LINE      CODE     ---       #1216
      01001678H   LINE      CODE     ---       #1217
      01001678H   LINE      CODE     ---       #1220
      01001699H   LINE      CODE     ---       #1221
      01001699H   LINE      CODE     ---       #1223
      0100169FH   LINE      CODE     ---       #1224
      0100169FH   LINE      CODE     ---       #1225
      010016A8H   LINE      CODE     ---       #1226
      010016AEH   LINE      CODE     ---       #1227
      010016B0H   LINE      CODE     ---       #1228
      010016B2H   LINE      CODE     ---       #1229
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 109


      010016B4H   LINE      CODE     ---       #1230
      010016B6H   LINE      CODE     ---       #1231
      010016BBH   LINE      CODE     ---       #1232
      010016BBH   LINE      CODE     ---       #1233
      010016BBH   LINE      CODE     ---       #1236
      010016BEH   LINE      CODE     ---       #1237
      010016BEH   LINE      CODE     ---       #1238
      010016C0H   LINE      CODE     ---       #1240
      010016DBH   LINE      CODE     ---       #1241
      010016DBH   LINE      CODE     ---       #1243
      010016DDH   LINE      CODE     ---       #1244
      010016DFH   LINE      CODE     ---       #1245
      010016E1H   LINE      CODE     ---       #1248
      010016EAH   LINE      CODE     ---       #1249
      010016F0H   LINE      CODE     ---       #1250
      010016F2H   LINE      CODE     ---       #1251
      010016F4H   LINE      CODE     ---       #1252
      010016F9H   LINE      CODE     ---       #1253
      01001700H   LINE      CODE     ---       #1254
      01001701H   LINE      CODE     ---       #1255
      01001710H   LINE      CODE     ---       #1256
      01001710H   LINE      CODE     ---       #1258
      01001716H   LINE      CODE     ---       #1259
      01001718H   LINE      CODE     ---       #1260
      0100171AH   LINE      CODE     ---       #1261
      0100171CH   LINE      CODE     ---       #1262
      0100171EH   LINE      CODE     ---       #1263
      0100171EH   LINE      CODE     ---       #1264
      0100171EH   LINE      CODE     ---       #1265
      ---         BLOCKEND  ---      ---       LVL=0

      ---         MODULE    ---      ---       ?C?FPMUL
      0100091FH   PUBLIC    CODE     ---       ?C?FPMUL

      ---         MODULE    ---      ---       ?C?FPDIV
      01000A28H   PUBLIC    CODE     ---       ?C?FPDIV

      ---         MODULE    ---      ---       ?C?FCAST
      01000ACFH   PUBLIC    CODE     ---       ?C?FCASTC
      01000ACAH   PUBLIC    CODE     ---       ?C?FCASTI
      01000AC5H   PUBLIC    CODE     ---       ?C?FCASTL

      ---         MODULE    ---      ---       PRINTF
      02000020H   PUBLIC    XDATA    ---       ?_PRINTF?BYTE
      02000020H   PUBLIC    XDATA    ---       ?_SPRINTF?BYTE
      0100119CH   PUBLIC    CODE     ---       _PRINTF
      01001196H   PUBLIC    CODE     ---       _SPRINTF

      ---         MODULE    ---      ---       ?C?FPGETOPN
      01000B03H   PUBLIC    CODE     ---       ?C?FPGETOPN2
      01000B38H   PUBLIC    CODE     ---       ?C?FPNANRESULT
      01000B42H   PUBLIC    CODE     ---       ?C?FPOVERFLOW
      01000B1AH   PUBLIC    CODE     ---       ?C?FPRESULT
      01000B2EH   PUBLIC    CODE     ---       ?C?FPRESULT2
      01000B3FH   PUBLIC    CODE     ---       ?C?FPUNDERFLOW

      ---         MODULE    ---      ---       ?C?FPROUND
      01000B4DH   PUBLIC    CODE     ---       ?C?FPROUND

      ---         MODULE    ---      ---       ?C?FPCONVERT
      01000B8AH   PUBLIC    CODE     ---       ?C?FPCONVERT

      ---         MODULE    ---      ---       ?C?FPADD
      01000C96H   PUBLIC    CODE     ---       ?C?FPADD
      01000C92H   PUBLIC    CODE     ---       ?C?FPSUB
LX51 LINKER/LOCATER V4.66.97.0                                                        07/12/2025  11:01:58  PAGE 110



      ---         MODULE    ---      ---       ?C?FTNPWR
      01000DB7H   PUBLIC    CODE     ---       ?C?FTNPWR

      ---         MODULE    ---      ---       ?C_INIT
      01001DE0H   PUBLIC    CODE     ---       ?C_START

      ---         MODULE    ---      ---       ?C?COPY
      01000EC7H   PUBLIC    CODE     ---       ?C?COPY

      ---         MODULE    ---      ---       ?C?CLDPTR
      01000EEDH   PUBLIC    CODE     ---       ?C?CLDPTR

      ---         MODULE    ---      ---       ?C?CLDOPTR
      01000F06H   PUBLIC    CODE     ---       ?C?CLDOPTR

      ---         MODULE    ---      ---       ?C?CSTPTR
      01000F33H   PUBLIC    CODE     ---       ?C?CSTPTR

      ---         MODULE    ---      ---       ?C?CSTOPTR
      01000F45H   PUBLIC    CODE     ---       ?C?CSTOPTR

      ---         MODULE    ---      ---       ?C?UIDIV
      01000F67H   PUBLIC    CODE     ---       ?C?UIDIV

      ---         MODULE    ---      ---       ?C?ILDIX
      01000FBCH   PUBLIC    CODE     ---       ?C?ILDIX

      ---         MODULE    ---      ---       ?C?ULDIV
      0100100EH   PUBLIC    CODE     ---       ?C?ULDIV

      ---         MODULE    ---      ---       ?C?LNEG
      010010A0H   PUBLIC    CODE     ---       ?C?LNEG

      ---         MODULE    ---      ---       ?C?LSTXDATA
      010010AEH   PUBLIC    CODE     ---       ?C?LSTXDATA

      ---         MODULE    ---      ---       ?C?LSTKXDATA
      010010BAH   PUBLIC    CODE     ---       ?C?LSTKXDATA

      ---         MODULE    ---      ---       ?C?PLDIXDATA
      010010EBH   PUBLIC    CODE     ---       ?C?PLDIXDATA

      ---         MODULE    ---      ---       ?C?PSTXDATA
      01001102H   PUBLIC    CODE     ---       ?C?PSTXDATA

      ---         MODULE    ---      ---       ?C?CCASE
      0100110BH   PUBLIC    CODE     ---       ?C?CCASE

Program Size: data=21.2 xdata=198 const=40 code=9927
LX51 RUN COMPLETE.  0 WARNING(S),  0 ERROR(S)
